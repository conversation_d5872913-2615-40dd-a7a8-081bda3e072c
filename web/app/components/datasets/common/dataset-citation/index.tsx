import React from 'react'
import { useTranslation } from 'react-i18next'
import { Switch } from 'antd'
import { InfoCircle } from '@/app/components/base/icons/src/vender/line/general'
import Tooltip from '@/app/components/base/tooltip'
import cn from '@/utils/classnames'

export type DatasetCitationProps = {
  /**
   * Whether the citation toggle is disabled
   */
  disabled?: boolean
  /**
   * Current citation enabled state
   */
  defaultChecked?: boolean
  /**
   * Callback when citation enabled state changes
   */
  onChange?: (checked: boolean) => void
  /**
   * Optional class name for styling
   */
  className?: string
  /**
   * Current citation enabled state
   */
  value?: boolean
}

const DatasetCitation = ({
  disabled = false,
  defaultChecked = false,
  onChange,
  className,
}: DatasetCitationProps) => {
  const { t } = useTranslation()

  return (
    <div className={cn('flex items-center justify-between py-2', className)}>
      <div className='flex items-center gap-1'>
        <span className='text-sm font-normal text-gray-700'>{t('dataset.citation.title')}</span>
        <Tooltip popupContent={t('dataset.citation.description')}>
          <InfoCircle className='w-3.5 h-3.5 text-gray-400' />
        </Tooltip>
      </div>
      <Switch
        disabled={disabled}
        defaultChecked={defaultChecked}
        onChange={onChange}
        size="small"
      />
    </div>
  )
}

export default DatasetCitation
