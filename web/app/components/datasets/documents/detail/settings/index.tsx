'use client'
import React, { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useBoolean } from 'ahooks'
import { useContext } from 'use-context-selector'
import { useRouter } from 'next/navigation'
import DatasetDetailContext from '@/context/dataset-detail'
import type { FullDocumentDetail } from '@/models/datasets'
import type { MetadataType } from '@/service/datasets'
import { fetchDocumentDetail } from '@/service/datasets'

import StepTwo from '@/app/components/datasets/create/step-two'
import StepsNavBar from '@/app/components/datasets/common/steps-nav-bar'
import AppUnavailable from '@/app/components/base/app-unavailable'
import { useDefaultModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import AccountSettingModal from '@/app/components/account-setting/account-setting-modal'

type DocumentSettingsProps = {
  datasetId: string
  documentId: string
}

const DocumentSettings = ({ datasetId, documentId }: DocumentSettingsProps) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { data: embeddingsDefaultModel } = useDefaultModel(ModelTypeEnum.textEmbedding)
  const { indexingTechnique, dataset } = useContext(DatasetDetailContext)

  // 是否显示api页面
  const [isShowSetAPIKey, { setTrue: showSetAPIKey, setFalse: hideSetAPIkey }] = useBoolean()
  // 是否存在错误
  const [hasError, setHasError] = useState(false)
  // 文档详情
  const [documentDetail, setDocumentDetail] = useState<FullDocumentDetail | null>(null)

  // 保存处理
  const saveHandler = () => router.push(`/datasets/${datasetId}/documents/${documentId}`)
  // 取消处理
  const cancelHandler = () => router.back()

  useEffect(() => {
    (async () => {
      try {
        const detail = await fetchDocumentDetail({
          datasetId,
          documentId,
          params: { metadata: 'without' as MetadataType },
        })
        setDocumentDetail(detail)
      }
      catch (e) {
        setHasError(true)
      }
    })()
  }, [datasetId, documentId])

  if (hasError)
    return <AppUnavailable code={500} unknownReason={t('datasetCreation.error.unavailable') as string} />
  return (
    <>
      <StepsNavBar onBack={saveHandler} backBtnText={documentDetail?.name || '--'} datasetId={datasetId} />
      <div className='h-[calc(100%-60px)]'>
        {dataset && documentDetail && (
          <StepTwo
            isAPIKeySet={!!embeddingsDefaultModel}
            datasetId={datasetId}
            dataSourceType={documentDetail.data_source_type}
            indexingType={indexingTechnique || ''}
            isSetting
            documentDetail={documentDetail}
            onSave={saveHandler}
            onCancel={cancelHandler}
            // @ts-expect-error 根本类型错误
            files={documentDetail.data_source_info?.upload_file ? [documentDetail.data_source_info.upload_file] : []}
            datasetFormInfo={{
              name: '',
              description: undefined,
            }} />
        )}
      </div>
      {isShowSetAPIKey && <AccountSettingModal onClose={hideSetAPIkey} activeTab="provider" />}
    </>
  )
}

export default DocumentSettings
