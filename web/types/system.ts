export enum Theme {
  light = 'light',
  dark = 'dark',
}

export enum SSOProtocol {
  SAML = 'saml',
  OIDC = 'oidc',
  OAuth2 = 'oauth2',
}

export type SystemFeatures = {
  sso_enforced_for_signin: boolean
  sso_enforced_for_signin_protocol: SSOProtocol | ''
  sso_enforced_for_web: boolean
  sso_enforced_for_web_protocol: SSOProtocol | ''
  enable_web_sso_switch_component: boolean
  enable_social_oauth_login: boolean
  is_allow_create_workspace: boolean
  is_allow_register: boolean
  // 是否为微服务
  is_micro: boolean
  // 是否sso控制
  enable_sso_login: boolean
  // 是否为私有化控制
  enable_private_register: boolean
  // 是否开启验证码登录
  enable_email_code_login: boolean
  // 是否开启密码登录
  enable_email_password_login: boolean
  // 是否支持国际化
  i18n_enabled: boolean
  // 是否启用xiyan的rag能力
  use_xiyan_server: boolean
  // 是否为应用解耦
  is_app_decoupled: boolean
  // 是否为新sso登录方式
  enable_local_login: boolean
  // 是否启用访问App鉴权
  enable_app_auth: boolean
  // 是否请求信息字段类型（header参数）
  enable_workflow_header: boolean
}
