'use client'
import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useMount } from 'ahooks'
import { debounce } from 'lodash-es'
import { Form, Radio, Select } from 'antd'

import MultiplSelect from '../../base/select/multiple-select'
import Modal from '../../base/modal'
import Button from '../../base/button'
import type { AppPublishConfig } from './type'
import style from './styles/style.module.css'
import { AppMarketCopyConfig, AppMarketPower } from '@/types/app-market'
import { ExclamationOutlined } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'
import { fetchMarketCategories } from '@/service/market'
import { useSystemContext } from '@/context/system-context'

const { Option } = Select

type MarketConfigModalProps = {
  appPublishConfig: AppPublishConfig
  onCancel: () => void
  onSave: (params: object) => void
}

const MarketConfigModal = ({
  appPublishConfig,
  onCancel,
  onSave,
}: MarketConfigModalProps) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const values = Form.useWatch([], form)
  // 是否为应用解耦
  const { isAppDecoupled } = useSystemContext()
  // 应用类别列表
  const [appCategoriesList, setAppCategoriesList] = useState<Array<any>>([])
  // 是否可提交
  const [disabled, setDisabled] = useState(false)

  // 表单初始化
  useEffect(() => {
    form.setFieldsValue({
      category_id: appPublishConfig.category_id,
      acl: appPublishConfig.acl || AppMarketPower.View,
      copyTool: appPublishConfig.tool_dataset_acl ? [AppMarketCopyConfig.both, AppMarketCopyConfig.tool].includes(appPublishConfig.tool_dataset_acl) : undefined,
      copyDataset: appPublishConfig.tool_dataset_acl ? [AppMarketCopyConfig.both, AppMarketCopyConfig.dataset].includes(appPublishConfig.tool_dataset_acl) : undefined,
    })
  }, [])

  // 获取应用种类列表
  const getAppCategoriesList = async () => {
    await fetchMarketCategories().then((res) => {
      setAppCategoriesList(
        res.map(item => ({
          value: item.id,
          label: item.name,
        })),
      )
    })
  }
  // 应用权限列表
  const appPermissionOptions = [{
    label: t('common.status.yes'),
    value: AppMarketPower.Copy,
  }, {
    label: t('common.status.no'),
    value: AppMarketPower.View,
  }]
  const copyToolOptions = [
    { label: t('app.publish.copy.tool'), value: true },
    { label: t('app.publish.copy.noTool'), value: false },
  ]
  const copyDatasetOptions = [
    { label: t('app.publish.copy.dataset'), value: true },
    { label: t('app.publish.copy.noDataset'), value: false },
  ]

  const handleSave = () => {
    const { category_id, acl, copyTool, copyDataset } = values
    const params = {
      category_id: category_id.join(),
      acl,
      tool_dataset_acl: '',
    }
    if (isAppDecoupled)
      params.acl = AppMarketPower.View

    if (acl === AppMarketPower.Copy) {
      if (copyTool && copyDataset)
        params.tool_dataset_acl = AppMarketCopyConfig.both
      else if (copyTool)
        params.tool_dataset_acl = AppMarketCopyConfig.tool
      else if (copyDataset)
        params.tool_dataset_acl = AppMarketCopyConfig.dataset
    }
    console.log('params', params)
    if (onSave)
      onSave(params)
  }

  useMount(() => {
    getAppCategoriesList()
  })

  useEffect(debounce(() => {
    form.validateFields({ validateOnly: true }).then(() => {
      setDisabled(false)
    },
    ).catch(() => {
      setDisabled(true)
    })
  }, 100), [form, values])

  return (
    <Modal
      isShow
      closable
      onClose={onCancel}
      title={t('app.action.publishMarket')}
      footer={
        <>
          <Button onClick={onCancel} className="mr-4" variant={'secondary-accent'}>{ t('common.operation.cancel') }</Button>
          <Button disabled={disabled} onClick={handleSave} variant={'primary'}>{ t('common.operation.save') }</Button>
        </>
      }
    >
      <Form form={form} layout='vertical' initialValues={{
        acl: AppMarketPower.View,
      }}>
        <Form.Item
          name="category_id"
          label={t('app.info.category')}
          rules={[{ required: true }]}
          validateTrigger='onChange'
        >
          <MultiplSelect
            options={appCategoriesList}
            placeholder={t('app.placeholder.appCategoriesPlaceholder')}
            maxTagCount='responsive'
            showSearch={false}
            maxCount={5}
          />
        </Form.Item>
        {!isAppDecoupled && <Form.Item
          name="acl"
          label={t('app.info.acl')}
          rules={[{ required: true }]}
          validateTrigger='onChange'
        >
          <Radio.Group options={appPermissionOptions}></Radio.Group>
        </Form.Item>}
        {
          values && values.acl === AppMarketPower.Copy && (
            <>
              <Form.Item
                name="copyTool"
                label={t('app.publish.copy.copyTool')}
                rules={[{ required: true }]}
                validateTrigger='onChange'
              >
                <Radio.Group options={copyToolOptions}></Radio.Group>
              </Form.Item>
              <Form.Item
                name="copyDataset"
                label={t('app.publish.copy.copyDataset')}
                rules={[{ required: true }]}
                validateTrigger='onChange'
              >
                <Radio.Group options={copyDatasetOptions}></Radio.Group>
              </Form.Item>
              <div className={style.configTip}>
                <ExclamationOutlined className='w-4 h-4 text-gray-G3 shrink-0' />
                {t('app.publish.tip.marketConfigTip')}
              </div>
            </>
          )
        }
      </Form>
    </Modal>
  )
}

export default MarketConfigModal
