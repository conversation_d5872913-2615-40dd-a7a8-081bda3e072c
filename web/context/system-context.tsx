'use client'

import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import dynamic from 'next/dynamic'

import useSWR from 'swr'
import { createContext, useContext, useContextSelector } from 'use-context-selector'
import type { FC, ReactNode } from 'react'
import { getSystemFeatures } from '@/service/common'
import { Theme } from '@/types/system'
import type {
  LangGeniusVersionResponse,
} from '@/models/common'
import type { SystemFeatures } from '@/types/system'
// 公共能力
import { STOP_SERVICE } from '@/config'
import Loading from '@/app/components/base/loading'
import { DEFAULT_SYSTEM_FEATURE } from '@/config/system'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'

const StopServiceModal = dynamic(() => import('@/app/components/account-setting/stop-service-modal'), { ssr: false })

export type SystemContextValue = {
  systemFeatures: SystemFeatures
  isPrivate: boolean
  canI18n: boolean
  langeniusVersionInfo: LangGeniusVersionResponse
  useSelector: typeof useSelector
  isSso?: boolean
  isMobile: boolean
  isAppDecoupled: boolean
  isNewSso: boolean
  isWorkflowHeader?: boolean
  isNeedAppAuth?: boolean
}

const initialLangeniusVersionInfo = {
  current_env: '',
  current_version: '',
  latest_version: '',
  release_date: '',
  release_notes: '',
  version: '',
  can_auto_update: false,
}

const SystemContext = createContext<SystemContextValue>({
  systemFeatures: DEFAULT_SYSTEM_FEATURE,
  canI18n: false,
  isPrivate: false,
  langeniusVersionInfo: initialLangeniusVersionInfo,
  useSelector,
  isSso: false,
  isMobile: false,
  isAppDecoupled: false,
  isWorkflowHeader: false,
  isNeedAppAuth: false,
  isNewSso: false,
})

export function useSelector<T>(selector: (value: SystemContextValue) => T): T {
  return useContextSelector(SystemContext, selector)
}

export type SystemContextProviderProps = {
  children: ReactNode
}

export const SystemContextProvider: FC<SystemContextProviderProps> = ({ children }) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  // 系统特征
  const { data: systemFeatures, isLoading } = useSWR({ url: '/console/system-features' }, getSystemFeatures, {
    fallbackData: DEFAULT_SYSTEM_FEATURE,
    revalidateOnFocus: false,
  })
  // 风格
  const [theme, setTheme] = useState<Theme>(Theme.light)
  // langenius版本信息
  const [langeniusVersionInfo, setLangeniusVersionInfo] = useState<LangGeniusVersionResponse>(
    initialLangeniusVersionInfo,
  )
  // 是否为私有化
  const isPrivate = useMemo(() => systemFeatures.enable_private_register, [systemFeatures])
  // 是否支持国际化
  const canI18n = useMemo(() => systemFeatures.i18n_enabled, [systemFeatures])
  // 是否为sso登录方式
  const isSso = useMemo(() => systemFeatures.enable_sso_login, [systemFeatures])
  // 是否为请求信息字段类型（header参数）
  const isWorkflowHeader = useMemo(() => systemFeatures.enable_workflow_header, [systemFeatures])
  // 访问应用是否需要鉴权
  const isNeedAppAuth = useMemo(() => systemFeatures.enable_app_auth, [systemFeatures])
  // 是否为应用解耦
  const isAppDecoupled = useMemo(() => systemFeatures.is_app_decoupled, [systemFeatures])
  // 是否为新sso登录方式
  const isNewSso = useMemo(() => systemFeatures.enable_local_login, [systemFeatures])

  // 变更风格
  const handleSetTheme = useCallback((theme: Theme) => {
    setTheme(theme)
    globalThis.document.documentElement.setAttribute('data-theme', theme)
  }, [])

  useEffect(() => {
    globalThis.document.documentElement.setAttribute('data-theme', theme)
  }, [theme])
  if (STOP_SERVICE)
    return <StopServiceModal></StopServiceModal>

  if (!systemFeatures || isLoading)
    return <Loading type='app' />

  return (
    <SystemContext.Provider value={{
      systemFeatures,
      langeniusVersionInfo,
      useSelector,
      isPrivate,
      canI18n,
      isSso,
      isMobile,
      isWorkflowHeader,
      isNeedAppAuth,
      isAppDecoupled,
      isNewSso,
    }}>
      {children}
    </SystemContext.Provider>
  )
}

export const useSystemContext = () => useContext(SystemContext)

export default SystemContext
