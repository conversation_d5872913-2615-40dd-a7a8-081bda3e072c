import type { FC } from 'react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { isEqual } from 'lodash-es'
import { Form } from 'antd'
import type { DataSet } from '@/models/datasets'
import { updateDatasetSetting } from '@/service/datasets'
import type { RetrievalConfig } from '@/types/datasets'
import type { Member } from '@/models/common'
// 知识库公共能力
import RetrievalMethodConfig from '@/app/components/datasets/common/retrieval-method-config'
import EconomicalRetrievalMethodConfig from '@/app/components/datasets/common/economical-retrieval-method-config'
import { ensureRerankModelSelected, isReRankModelSelected } from '@/app/components/datasets/common/check-rerank-model'
// 账户设置公共能力
import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'
import {
  useModelList,
  useModelListAndDefaultModelAndCurrentProviderAndModel,
} from '@/app/components/account-setting/model-provider-page/hooks'
import type { DefaultModel } from '@/app/components/account-setting/model-provider-page/declarations'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
// 公共组件
import cn from '@/utils/classnames'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import { CloseOutlined } from '@/app/components/base/icons/src/public/common'
import { AlertTriangle } from '@/app/components/base/icons/src/vender/solid/alertsAndFeedback'
import { useToastContext } from '@/app/components/base/toast'
import { useProviderContext } from '@/context/provider-context'
import DatasetCitation from '@/app/components/datasets/common/dataset-citation'

type SettingsModalProps = {
  isShowSettingsModal: boolean
  currentDataset: DataSet
  onCancel: () => void
  onSave: (newDataset: DataSet) => void
  disabled?: boolean
}

// 行类
const rowClass = `
  flex justify-between py-3 flex-wrap gap-y-2
`
// 标签
const labelClass = `
  flex w-[168px] shrink-0
`

const SettingsModal: FC<SettingsModalProps> = ({
  isShowSettingsModal,
  currentDataset,
  onCancel,
  onSave,
  disabled,
}) => {
  const { data: embeddingsModelList, mutate: mutateEmbeddingModelList, isValidating: isEmbeddingModelListLoading } = useModelList(ModelTypeEnum.textEmbedding)

  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  const { t } = useTranslation()
  const { notify } = useToastContext()
  const [form] = Form.useForm()
  const { useXIYANRag } = useProviderContext()

  // 保存加载
  const [loading, setLoading] = useState(false)
  const [localeCurrentDataset, setLocaleCurrentDataset] = useState({ ...currentDataset })
  // 选中的成员id
  const [selectedMemberIDs, setSelectedMemberIDs] = useState<string[]>(currentDataset.partial_member_list || [])
  // 成员列表
  const [memberList, setMemberList] = useState<Member[]>([])
  // 索引方法
  const [indexMethod, setIndexMethod] = useState(currentDataset.indexing_technique)
  // 检索配置
  const [retrievalConfig, setRetrievalConfig] = useState(localeCurrentDataset?.retrieval_model_dict as RetrievalConfig)
  // 是否隐藏变更tip
  const [isHideChangedTip, setIsHideChangedTip] = useState(true)
  // 检索方式是否变化
  const isRetrievalChanged = !isEqual(retrievalConfig, localeCurrentDataset?.retrieval_model_dict) || indexMethod !== localeCurrentDataset?.indexing_technique
  // 内置模型
  const [embeddingModel, setEmbeddingModel] = useState<DefaultModel>(
    currentDataset?.embedding_model
      ? {
        provider: localeCurrentDataset.embedding_model_provider,
        model: localeCurrentDataset.embedding_model,
      }
      : {
        provider: '',
        model: '',
      },
  )
  // Add citation state
  const [citationEnabled, setCitationEnabled] = useState(currentDataset.citation_enabled || false)

  // 保存知识库配置
  const handleSave = async () => {
    if (loading)
      return
    form.validateFields().then(async (values) => {
      if (
        !isReRankModelSelected({
          rerankDefaultModel,
          isRerankDefaultModelValid: !!isRerankDefaultModelValid,
          rerankModelList,
          retrievalConfig,
          indexMethod,
        })
      ) {
        notify({ type: 'error', message: t('dataset.notify.rerankModelRequired') })
        return
      }
      const postRetrievalConfig = ensureRerankModelSelected({
        rerankDefaultModel: rerankDefaultModel!,
        retrievalConfig,
        indexMethod,
      })
      try {
        setLoading(true)
        const { id, name, description, permission } = localeCurrentDataset
        const requestParams = {
          datasetId: id,
          body: {
            name,
            description,
            permission,
            indexing_technique: indexMethod,
            retrieval_model: {
              ...postRetrievalConfig,
              score_threshold: postRetrievalConfig.score_threshold,
              score_threshold_enabled: true,
            },
            embedding_model: embeddingModel.model,
            embedding_model_provider: embeddingModel.provider,
            citation_enabled: citationEnabled,
          },
        } as any
        if (permission === 'partial_members') {
          requestParams.body.partial_member_list = selectedMemberIDs.map((id) => {
            return {
              user_id: id,
              role: memberList.find(member => member.id === id)?.role,
            }
          })
        }
        await updateDatasetSetting(requestParams)
        notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
        onSave({
          ...localeCurrentDataset,
          indexing_technique: indexMethod,
          retrieval_model_dict: postRetrievalConfig,
          embedding_model: embeddingModel.model,
          embedding_model_provider: embeddingModel.provider,
          citation_enabled: citationEnabled,
        })
      }
      catch (e) {
        notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      }
      finally {
        setLoading(false)
      }
    })
  }

  return (
    <Modal
      title={t('dataset.action.config')}
      isShow={isShowSettingsModal}
      onClose={onCancel}
      closable
      footer={(
        <div className='flex items-center justify-end space-x-4'>
          <Button
            onClick={onCancel}
            variant={'secondary-accent'}
            className='mr-4'
          >
            {t('common.operation.cancel')}
          </Button>
          <Button
            variant='primary'
            disabled={loading}
            onClick={handleSave}
          >
            {t('common.operation.save')}
          </Button>
        </div>
      )}
    >
      {/* Body */}
      <div className='pb-[24px]'>
        {/* 索引方式 */}
        <div className={rowClass}>
          <div className={cn(labelClass, 'w-auto min-w-[168px]')}>
            <div>{t('dataset.action.setting')}</div>
          </div>
          <div className='w-full'>
            {indexMethod === 'high_quality'
              ? (
                <RetrievalMethodConfig
                  value={retrievalConfig}
                  onChange={setRetrievalConfig}
                />
              )
              : (
                <EconomicalRetrievalMethodConfig
                  value={retrievalConfig}
                  onChange={setRetrievalConfig}
                />
              )}
          </div>
        </div>
        {/* 引用和归属 */}
        <DatasetCitation
          disabled={disabled}
          value={citationEnabled}
          onChange={setCitationEnabled}
          className={rowClass}
        />

        {/* 模型 */}
        {indexMethod === 'high_quality' && (
          <div className={cn(rowClass)}>
            <div className={labelClass}>
              {t('dataset.info.embeddingModel')}
            </div>
            <div className='w-full h-9 rounded'>
              <ModelSelector
                readonly={useXIYANRag}
                loading={isEmbeddingModelListLoading}
                triggerClassName=''
                defaultModel={embeddingModel}
                modelList={embeddingsModelList}
                onSelect={(model: DefaultModel) => {
                  setEmbeddingModel(model)
                }}
                onFetch={mutateEmbeddingModelList}
              />
            </div>
          </div>
        )}
      </div>
      {isRetrievalChanged && !isHideChangedTip && (
        <div className='absolute z-10 left-[30px] right-[30px] bottom-[76px] flex h-10 items-center px-3 rounded border border-[#FEF0C7] bg-[#FFFAEB] shadow-lg justify-between'>
          <div className='flex items-center'>
            <AlertTriangle className='mr-1 w-3 h-3 text-[#F79009]' />
            <div className='leading-[18px] text-xs font-semibold text-gray-700'>{t('dataset.notify.retrieveChangeTip')}</div>
          </div>
          <div className='p-1 cursor-pointer' onClick={(e) => {
            setIsHideChangedTip(true)
            e.stopPropagation()
            e.nativeEvent.stopImmediatePropagation()
          }}>
            <CloseOutlined className='w-4 h-4 text-gray-500 ' />
          </div>
        </div>
      )}
    </Modal>
  )
}

export default SettingsModal
