'use client'
import React from 'react'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'

import cn from 'classnames'
import DatasetConfig from './dataset-config'
import AgentTools from './agent-tools'
import ConfigContext from '@/context/debug-configuration'
import { type PromptVariable } from '@/models/debug'
import type { InputVar } from '@/app/components/workflow/types'
import style from '@/app/components/app/configuration/styles/style.module.scss'

import type { OnFeaturesChange } from '@/app/components/base/features/types'
import ConversationOpener from '@/app/components/base/features/new-feature-panel/conversation-opener'
import FollowUp from '@/app/components/base/features/new-feature-panel/follow-up'
// import Citation from '@/app/components/base/features/new-feature-panel/citation'
import ChatBg from '@/app/components/base/features/new-feature-panel/chat-bg'
import NewFeaturePanel from '@/app/components/base/features/new-feature-panel'
import NewVoiceInput from '@/app/components/base/features/new-feature-panel/new-voice-input'
import NewVoiceConversation from '@/app/components/base/features/new-feature-panel/new-voice-conversation'
import FeatureCollapseGroup from '@/app/components/base/features/feature-collapse-group'
import Scrollbar from '@/app/components/base/scrollbar'
// import type { NewLanguageType, NewVoiceConfigType } from '@/models/app'
import { useSystemContext } from '@/context/system-context'

// import HistoryPanel from '../config-prompt/conversation-history/history-panel'
// import ConfigVar from '@/app/components/app/configuration/config-var'
// import { ModelModeType } from '@/types/app'

type FeatureListProp = {
  isChatMode: boolean
  disabled: boolean
  onChange?: OnFeaturesChange
  workflowVariables?: InputVar[]
  onAutoAddPromptVariable?: (variable: PromptVariable[]) => void
}

// 自主规划显示的feature
const FeatureList = ({
  isChatMode,
  disabled,
  onChange,
  workflowVariables,
  onAutoAddPromptVariable,
}: FeatureListProp) => {
  const { t } = useTranslation()
  const { isPrivate } = useSystemContext()
  const {
    mode,
    isAgent,
    modelConfig,
    isVoiceCalling,
    voicesConfigData,
  } = useContext(ConfigContext)
  // const isChatApp = ['advanced-chat', 'agent-chat', 'chat'].includes(mode)

  const promptVariables = modelConfig.configs.prompt_variables

  // const handlePromptVariablesNameChange = (newVariables: PromptVariable[]) => {
  //   setPrevPromptConfig(modelConfig.configs)
  //   const newModelConfig = produce(modelConfig, (draft: ModelConfig) => {
  //     draft.configs.prompt_variables = newVariables
  //   })
  //   setModelConfig(newModelConfig)
  // }

  return (
    <>
      <Scrollbar className={cn(style.content, style.flexColGap20)}>
        <div className={cn('title-14-24')}>{t('appDebug.pageTitle.feature')}</div>

        {/* 二期需求 去掉变量 */}
        {/* Variables */}
        {/* <ConfigVar
          promptVariables={promptVariables}
          onPromptVariablesChange={handlePromptVariablesNameChange}
        /> */}

        {/* 知识 */}
        <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.dataSet')}>
          <DatasetConfig />
        </FeatureCollapseGroup>

        {/* 工具 */}
        <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.tools')}>
          {isAgent && (
            <AgentTools />
          )}
          <NewFeaturePanel
            inWorkflow={false}
            showFileUpload={false}
            isChatMode={mode !== 'completion'}
            disabled={false}
            onChange={onChange}
          />
        </FeatureCollapseGroup>

        {/* 对话 */}
        <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.conversation')}>
          {/* Chat History 对话历史 */}
          {/* {isAdvancedMode && isChatApp && modelModeType === ModelModeType.completion && (
            <HistoryPanel
              showWarning={!hasSetBlockStatus.history}
              onShowEditModal={showHistoryModal}
            />
          )} */}
          {/* 对话开场白 */}
          {isChatMode && (
            <ConversationOpener
              disabled={disabled}
              onChange={onChange}
              promptVariables={promptVariables}
              workflowVariables={workflowVariables}
              onAutoAddPromptVariable={onAutoAddPromptVariable}
            />
          )}
          {/* 追问 下一步问题与建议 */}
          {isChatMode && (
            <FollowUp disabled={disabled} onChange={onChange} />
          )}
          {/* 引用和归属 */}
          {/* {isChatMode && (
            <Citation disabled={disabled} onChange={onChange} />
          )} */}
          {/* 会话背景图 */}
          {isChatMode && (
            <ChatBg disabled={disabled} onChange={onChange}></ChatBg>
          )}
          {/* </FeatureCollapseGroup> */}
          {/* 自主规划-语音 */}
          {/* <FeatureCollapseGroup title={t('appDebug.featureCollapseGroup.title.voice')}> */}
          {/* 语音输入 */}
          {isChatMode && !isPrivate && (
            <NewVoiceInput
              disabled={disabled}
              onChange={onChange}
              promptVariables={promptVariables}
              workflowVariables={workflowVariables}
              onAutoAddPromptVariable={onAutoAddPromptVariable}
              applyType="selfPlanning"
              isAutoPlay={false}
              voicesConfigData={voicesConfigData}
            />
          )}
          {/* 语音对话 */}
          {isChatMode && !isPrivate && (
            <NewVoiceConversation
              disabled={disabled || isVoiceCalling}
              onChange={onChange}
              promptVariables={promptVariables}
              workflowVariables={workflowVariables}
              onAutoAddPromptVariable={onAutoAddPromptVariable}
              applyType="selfPlanning"
              voicesConfigData={voicesConfigData}
            />
          )}
        </FeatureCollapseGroup>

        {/* 工具箱在new-feature-panel里 */}

      </Scrollbar>
    </>
  )
}
export default React.memo(FeatureList)
