const translation = {
  title: '知识库',
  externalTag: '外部',
  datasetMenus: {
    documents: '文件',
    hitTesting: '命中测试',
    settings: '知识库设置',
  },
  // 动作
  action: {
    createDataset: '创建知识库',
    config: '配置选择',
    setting: '知识检索设置',
    learnMore: '了解更多',
    retrievalSettings: '召回设置',
    rerankSettings: 'Rerank 设置',
  },
  // 弹窗标题
  modalTitle: {
    deleteDatasetConfirmTitle: '知识库删除',
    datasetSetting: '知识库编辑',
  },
  // 提示和公告
  notify: {
    unavailableTip: '由于 embedding 模型不可用，需要配置默认 embedding 模型',
    defaultRetrievalTip: '默认情况下使用多路召回。从多个知识库中检索知识，然后重新排序。',
    mixtureHighQualityAndEconomicTip: '混合使用高质量和经济型知识库需要配置 Rerank 模型。',
    inconsistentEmbeddingModelTip: '当所选知识库配置的 Embedding 模型不一致时，需要配置 Rerank 模型。',
    docsFailedNotice: '文档无法被索引',
    embeddingExpire: 'embedding模型下架，去知识库设置处变更embedding模型',
    datasetFull: '最多可创建{{num}}个知识库，请删除后重试。',
    noDataset: '没有可用的知识库',
    datasetDeleted: '知识库已删除',
    datasetUsedByApp: '某些应用正在使用该知识库。应用将无法再使用该知识库,所有的提示配置和日志将被永久删除。',
    deleteDatasetConfirmContent: '删除知识库后，调用该知识库的工具和应用会同步失效。删除操作不可逆，是否确认删除?',
    retrieveChangeTip: '修改索引模式和检索模式可能会影响与该知识库关联的应用程序。',
    knowledgeTip: '点击 “+” 按钮添加知识库',
    rerankModelRequired: '请选择 Rerank 模型',
    addTip: '添加知识库后大模型将基于上传的文档提供更加精准的回答',
    nameRepeat: '知识库名称重复',
    nameError: '知识库名称不符合要求',
  },
  // 输入占位符
  placeholder: {
    namePlaceholder: '请输入知识库名称',
    descPlaceholder: '描述这个知识库中的内容。详细的描述可以让 AI 及时访问知识库的内容。如果为空，智能体平台 将使用默认的命中策略。',
    rerankModel: '请选择 Rerank 模型',
  },
  info: {
    unavailable: '不可用',
    name: '知识库名称',
    desc: '知识库描述',
    indexMethod: '索引模式',
    retrievalTip: '关于检索方法。',
    embeddingModel: 'Embedding 模型设置',
    embeddingModelTip: '修改 Embedding 模型，请去',
    knowledgeEnhancement: '知识增强',
    securityReason: '原因：',
    securitySuggestion: '建议：',
    unselectable: '已创建知识库，无法更改模型！',
    createDatasetUnselectableTip: '模型暂不支持更换！',
  },
  retrieval: {
    semantic_search: {
      title: '语义检索',
      description: '通过匹配文本的内容相似度，检索文本分段。',
    },
    full_text_search: {
      title: '全文检索',
      description: '全文检索处理文本，分词建索引，快搜关键词，排序相关文档。',
    },
    hybrid_search: {
      title: '混合检索',
      description: '混合检索是语义检索与全文检索的融合，提高检索结果的质量和准确性。',
      recommend: '推荐',
    },
    invertedIndex: {
      title: '倒排索引',
      description: '倒排索引是一种用于高效检索的结构。按术语组织，每个术语指向包含它的文档或网页',
    },
    parameter_settings: {
      title: '参数设置',
      description: '参数设置是用于高效检索的结构。按术语组织，每个术语指向包含它的文档或网页',
    },
  },
  indexingTechnique: {
    high_quality: '高质量',
    high_quality_tip: '调用 Embedding 模型进行处理，以在用户查询时提供更高的准确度。',
    economy: '经济',
    economy_tip: '使用离线的向量引擎、关键词索引等方式，降低了准确度但无需消耗 Token',
  },
  indexingMethod: {
    semantic_search: '向量检索',
    full_text_search: '全文检索',
    hybrid_search: '混合检索',
    invertedIndex: '倒排索引',
  },
  weightedScore: {
    title: '权重设置',
    description: '通过调整分配的权重，重新排序策略确定是优先进行语义匹配还是关键字匹配。',
    semantic: '语义',
    keyword: '关键词',
  },
  permission: {
    permissions: '可见权限',
    permissionsOnlyMe: '只有我',
    permissionsAllMember: '所有团队成员',
    permissionsInvitedMembers: '部分团队成员',
    me: '（你）',
  },
  // 召回测试
  hit: {
    recents: '命中历史',
    input: '命中文案',
    source: '检索区域',
    text: '检索文案',
    textPlaceholder: '输入期望查询的文字内容', // 提示：请输入期望查询的文字内容
    time: '命中时间',
    sourceValue: {
      app: '应用',
      hit_testing: '知识库',
      plugin: '工具',
    },
    result: '命中结果',
    hitSeg: '命中切片',
    score: '分值：',
    customSeg: '自动切片',
    emptyTip: '命中的片段会在该区域展示',
    viewChart: '查看向量图表',
    viewDetail: '查看详情',
  },
  // 参数配置
  paramConfig: {
    top_k: '最大召回片段数',
    top_kTip: '在大量数据中找出前K个与检索词最相关或最重要的结果，k为用户填写数值。',
    score_threshold: '相似度匹配值',
    score_thresholdTip: '衡量检索结果与检索词之间相关性或匹配程度的一个量化指标。得分越高，表示该结果与检索词越相关或匹配程度越高数据取值为0-1之间。',
  },
  // 引用和归属
  citation: {
    title: '引用和归属',
    description: '启用后，在使用该知识库的应用中将显示引用来源',
  },

}

export default translation
