const translation = {
  steps: {
    header: {
      creation: '创建知识库',
      update: '上传文件',
    },
  },
  // 步骤二
  stepTwo: {
    configSelect: '配置选择',
    segmentation: '分段处理',
    auto: '自动分段与清洗',
    previewContent: '分段预览展示区',
    autoDescription: '按照默认方式针对上传的文件进行分段处理，无需关注分段规则',
    custom: '自定义',
    customDescription: '自定义分段规则、分段长度以及预处理规则等参数',
    separator: '分段标识符',
    separatorPlaceholder: '\\n\\n 用于分段；\\n 用于分行',
    maxLength: '分段最大长度',
    maxLengthTip: '使用字节计算规则为1个中文汉字两个字节，1个英文字母1个字节',
    overlap: '分段重叠长度',
    overlapTip: '通过合理设置重叠长度，可以保留分段之间的语义关系，提高信息连贯性，并优化处理效果。',
    overlapCheck: '分段重叠长度不能大于分段最大长度',
    rules: '文本预处理规则',
    removeExtraSpaces: '替换掉连续的空格、换行符和制表符',
    removeUrlEmails: '删除所有 URL 和电子邮件地址',
    removeStopwords: '去除停用词，例如 “a”，“an”，“the” 等',
    preview: '确认并预览',
    estimateSegment: '预估分段数',
    calculating: '计算中...',
    fileSource: '预处理文档',
    previewTitle: '分段预览',
    indexSettingTip: '要更改索引方法和 embedding 模型，请转到',
    datasetSettingLink: '知识库设置。',
    viewAll: '查看全部',
    repackUp: '收起全部',
    structuredDataCheck: '请至少一列参与检索',
    setting: '知识检索设置',
    dataSelect: '数据选择',
    table: '表结构',
    form: {
      col: '列名',
      canIndex: '是否参与检索',
      beginLine: '起始行',
      header: '表头',
      sheet: '数据表',
    },
    knowledgeEnhancement: {
      title: '知识增强',
      description: '在检索问答时，系统通过检索知识点召回对应的切片。开启知识增强，会调用大模型抽取更加丰富的知识点，增加切片的召回率。开启后，会增加文档的处理时长和资源消耗',
      enhanceContent: {
        title: '增强内容',
        paragraphSummary: '段落总结',
        problemGeneration: '问题生成',
        knowledgeGraph: '知识图谱',
      },
      modelSelect: '大模型',
    },
    analyticMethod: {
      title: '解析方式',
      rapidAnalysis: '快速解析',
      rapidDescription: '不会识别文档中的图像元素，适用于纯文本',
      accurateAnalysis: '精准解析',
      accurateDescription: '支持文档中图片提取，适用于图片型PDF',
      analyticTip: '精准解析只支持pdf文件，由于上传的文件不包含pdf文件，建议更换为快速解析',
    },
    enhancedFeatureTip: '您已选择打开知识增强功能，该功能为必选项，需选择才能保存',
    rapidAnalysis: '快速分析',
    rapidAnalysisDescription: '不会对识别文档中图像元素，适用于纯文本',
    precisionAnalysis: '精准分析',
    precisionAnalysisDescription: '支持文档中图片提取，适用于图片型PDF',
  },
  // 步骤一
  stepOne: {
    filePreview: '文件预览',
    form: {
      name: '知识库名称',
      namePlaceholder: '请输入知识库名称',
      nameDescription: '知识库名称仅支持中文、英文、数字、下划线(_)、中划线(-)、英文点(.)(1~20字符)',
      desc: '知识库描述',
      descPlaceholder: '请输入知识库描述',
      uploader: '数据上传',
      // 知识库数据
      dataType: {
        title: '数据类型',
        fileTypeError: '该数据类型只支持{{supportType}}文件',
        structured: '结构化数据',
        unstructured: '非结构化数据',
        structuredDesc: '文件的主要内容为结构化文本，需具备明确的字段约束，如问答总结、政策条款、数据收集等，支持csv、xlsx、xls 格式',
        unstructuredDesc: '文件的主要内容为文本和图表，如文章、报告、书籍等，支持TXT、MARKDOWN、PDF、DOCX格式',
        structuredTip: '支持  CSV、XLSX、XLS格式，每个文件不超过15mb，最多同时上传50个文件，不超过10万行，20列，每行不超过15万字，目文件中最多支持一个sheet工作表(超出范围的内容会被自动忽略)',
      },
    },
  },
  error: {
    unavailable: '该知识库不可用',
  },
  stepThree: {
    creationTitle: '🎉 知识库已创建',
    label: '知识库名称',
    additionTitle: '🎉 文档已上传',
    additionP1: '文档已上传至知识库：',
    additionP2: '，你可以在知识库的文档列表中找到它。',
    stop: '停止处理',
    resume: '恢复处理',
    navTo: '前往文档',
    toApps: '开发应用',
    sideTipTitle: '接下来做什么',
    sideTipContent: '当文档完成索引处理后，知识库即可集成至应用内作为上下文使用，你可以在提示词编排页找到上下文设置。你也可以创建成可独立使用的 ChatGPT 索引插件发布。',
    modelTitle: '确认停止索引过程吗？',
    modelContent: '如果您需要稍后恢复处理，则从停止处继续。',
    modelButtonConfirm: '确认停止',
    modelButtonCancel: '取消',
    parsing: '处理中',
    completed: '处理完成',
    viewAll: '查看全部',
    repackUp: '重新打包',
    retry: '重试',
  },
}

export default translation
