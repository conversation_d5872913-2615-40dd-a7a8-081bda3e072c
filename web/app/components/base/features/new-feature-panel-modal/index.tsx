import React, { useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import produce from 'immer'
import ChatBg from '../new-feature-panel/chat-bg'
import { useFeaturesStore } from '../hooks'
import DrawerModel from './drawerModel'
import DialogWrapper from '@/app/components/base/features/new-feature-panel/dialog-wrapper'
import type { OnFeaturesChange } from '@/app/components/base/features/types'

import ConversationOpener from '@/app/components/base/features/new-feature-panel/conversation-opener'
import FollowUp from '@/app/components/base/features/new-feature-panel/follow-up'
import FileUpload from '@/app/components/base/features/new-feature-panel/file-upload'
import ImageUpload from '@/app/components/base/features/new-feature-panel/image-upload'
import ChatVariablePanel from '@/app/components/workflow/panel/chat-variable-panel'
import EnvPanel from '@/app/components/workflow/panel/env-panel'
// import ChatVariableButton from './chat-variable-button'
// import EnvButton from './env-button'
// import Scrollbar from '@/app/components/base/scrollbar'
import NewVoiceConversation from '@/app/components/base/features/new-feature-panel/new-voice-conversation'
import NewVoiceInput from '@/app/components/base/features/new-feature-panel/new-voice-input'

import type { PromptVariable } from '@/models/debug'
import type { InputVar } from '@/app/components/workflow/types'

import { useVoicelists } from '@/app/components/workflow/hooks'
import DslImportExport from '@/app/components/base/features/new-feature-panel/dsl-import-export'
import { useSystemContext } from '@/context/system-context'

type Props = {
  show: boolean
  isChatMode: boolean
  disabled: boolean
  onChange?: OnFeaturesChange
  onClose: () => void
  showFileUpload?: boolean
  promptVariables?: PromptVariable[]
  workflowVariables?: InputVar[]
  onAutoAddPromptVariable?: (variable: PromptVariable[]) => void
}

// 工作流中显示的feature inWorkflow=true
const NewFeaturePanelModal = ({
  show,
  isChatMode,
  disabled,
  onChange,
  onClose,
  showFileUpload = true,
  promptVariables,
  workflowVariables,
  onAutoAddPromptVariable,
}: Props) => {
  const { t } = useTranslation()
  const { isPrivate } = useSystemContext()
  const featurePanelModalRef = useRef<HTMLDivElement>(null)
  const { languageList, voicesConfigData, timbreDefaulValue, languageDefaulVal } = useVoicelists()
  const {
    features,
    setFeatures,
  } = useFeaturesStore()!.getState()

  // 是否是长文档
  const isLongWeb = useMemo(() => {
    if (!workflowVariables)
      return false
    return workflowVariables?.find(item => item.type === 'longTxt')
  }, [workflowVariables])

  useEffect(() => {
    const newFeatures = produce(features, (draft) => {
      draft.text2speech!.voice_input = {
        ...draft.text2speech!.voice_input,
        language: draft.text2speech?.voice_input?.language || languageDefaulVal,
        timbre: draft.text2speech?.voice_input?.timbre || timbreDefaulValue,
      }
      draft.text2speech!.voice_conversation = {
        ...draft.text2speech!.voice_conversation,
        language: draft.text2speech?.voice_conversation?.language || languageDefaulVal,
        timbre: draft.text2speech?.voice_conversation?.timbre || timbreDefaulValue,
      }
    })
    setFeatures(newFeatures)
  }, [features, languageDefaulVal, setFeatures, timbreDefaulValue])
  return (
    <DialogWrapper
      show={show}
      title={t('workflow.common.features') || ''}
      onClose={onClose}
    >
      <div className='h-full grow basis-0 overflow-y-auto space-y-2' ref={featurePanelModalRef}>
        {/* {!isChatMode && !inWorkflow && (
          <MoreLikeThis disabled={disabled} onChange={onChange} />
        )} */}
        {/* 文件上传 */}
        {showFileUpload && isChatMode && <FileUpload disabled={disabled} onChange={onChange} />}
        {/* 图片上传 */}
        {showFileUpload && !isChatMode && <ImageUpload disabled={disabled} onChange={onChange} />}
        {/* 引用和归属 */}
        {/* {isChatMode && (
          <Citation inWorkflow={inWorkflow} disabled={disabled} onChange={onChange} />
        )} */}
        {isChatMode && (
          <ConversationOpener
            disabled={disabled}
            onChange={onChange}
          />
        )}
        {isChatMode && (
          <FollowUp disabled={disabled} onChange={onChange} />
        )}
        {/* 会话背景图 */}
        {isChatMode && <ChatBg inWorkflow={inWorkflow} disabled={disabled} onChange={onChange}></ChatBg>}
        {/* 工作流-语音输入 */}
        {isChatMode && !isLongWeb && !isPrivate && (
          <NewVoiceInput
            disabled={disabled}
            onChange={onChange}
            applyType='workflow'
            promptVariables={promptVariables}
            workflowVariables={workflowVariables}
            onAutoAddPromptVariable={onAutoAddPromptVariable}
            isAutoPlay={false}
            voicesConfigData={voicesConfigData}
          />
        )}
        {/* 工作流-语音对话 */}
        {isChatMode && !isLongWeb && !isPrivate && (
          <NewVoiceConversation
            disabled={disabled}
            onChange={onChange}
            applyType='workflow'
            promptVariables={promptVariables}
            workflowVariables={workflowVariables}
            onAutoAddPromptVariable={onAutoAddPromptVariable}
            voicesConfigData={voicesConfigData}
          />
        )}
        {/* {text2speechDefaultModel && (isChatMode || !inWorkflow) && (
          <TextToSpeech disabled={disabled} onChange={onChange} />
        )} */}
        {/* 语音转文字 */}
        {/* {isChatMode && speech2textDefaultModel && (
          <SpeechToText inWorkflow={inWorkflow} disabled={disabled} onChange={onChange} />
        )} */}

        {/* {(isChatMode || !inWorkflow) && <Moderation disabled={disabled} onChange={onChange} />} */}
        {/* 标注回复 */}
        {/* {!inWorkflow && isChatMode && (
          <AnnotationReply disabled={disabled} onChange={onChange} />
        )} */}
        {/* 环境面板 */}
        <EnvPanel />
        {workflowVariables?.map(item => item.type === 'longTxt' && <DrawerModel key={item.type} />)}
        {/* 对话变量 */}
        {isChatMode && <ChatVariablePanel />}
        {/* dsl导入导出能力 */}
        <DslImportExport />
      </div>
    </DialogWrapper>
  )
}

export default NewFeaturePanelModal
