import type { FC } from 'react'
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import useSWR from 'swr'
import { useContext } from 'use-context-selector'
import { useTranslation } from 'react-i18next'
import { omit } from 'lodash-es'
import { Divider, Progress } from 'antd'
import SegmentCard from '../segment-card'
import { DocumentContext } from '../index'
import s from './style.module.css'

import type {
  FullDocumentDetail,
  IndexingStatusResponse,
  PreProcessingRule,
  ProcessRuleResponse,
} from '@/models/datasets'
import type { CommonResponse } from '@/models/common'
import {
  fetchIndexingStatus as doFetchIndexingStatus,
  fetchProcessRule,
  pauseDocIndexing,
  resumeDocIndexing,
} from '@/service/datasets'
import datasetStyle from '@/app/components/datasets/styles/style.module.css'
import FieldInfo from '@/app/components/datasets/common/field-info'
import { judgeDocIndexEnd } from '@/app/components/datasets/utils'
import { asyncRunSafe, sleep } from '@/utils'
import cn from '@/utils/classnames'
import { ToastContext } from '@/app/components/base/toast'
import Button from '@/app/components/base/button'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import DatasetDetailContext from '@/context/dataset-detail'

/**
 * 文档处理状态类型定义
 * waiting: 等待中
 * queuing: 排队中
 * indexing: 索引中
 * splitting: 分段中
 * parsing: 解析中
 * cleaning: 清理中
 * paused: 已暂停
 * completed: 已完成
 * error: 错误
 */
type DocStatus = 'waiting' | 'queuing' | 'indexing' | 'splitting' | 'parsing' | 'cleaning' | 'paused' | 'completed' | 'error'

/**
 * 文档状态分组常量
 * WAITING: 等待状态组（等待中、排队中）
 * PROCESSING: 处理中状态组（索引中、分段中、解析中、清理中）
 * PAUSED: 暂停状态组
 * COMPLETED: 完成状态组
 * ERROR: 错误状态组
 */
const DOC_STATUS = {
  WAITING: ['waiting', 'queuing'] as const,
  PROCESSING: ['indexing', 'splitting', 'parsing', 'cleaning'] as const,
  PAUSED: ['paused'] as const,
  COMPLETED: ['completed'] as const,
  ERROR: ['error'] as const,
} as const

/**
 * 状态切换按钮组件属性定义
 */
type Props = {
  detail?: FullDocumentDetail // 文档详情
  datasetId?: string // 数据集ID
  documentId?: string // 文档ID
  detailUpdate: VoidFunction // 更新详情的回调函数
  onSeeDocumentInfo: () => void // 查看文档信息的回调函数
}

/**
 * 状态切换按钮组件
 * 根据当前状态显示不同的按钮文案和状态
 */
const StatusSwitchButton: FC<{
  status: DocStatus | undefined // 当前状态
  isLoading: boolean // 是否加载中
  isSwitching: boolean // 是否正在切换状态
  isManuallyResuming: boolean // 是否手动恢复中
  onSwitch: () => void // 切换状态的回调函数
}> = ({ status, isLoading, isSwitching, isManuallyResuming, onSwitch }) => {
  const { t } = useTranslation()

  // 判断当前状态
  const isProcessing = status && DOC_STATUS.PROCESSING.includes(status as any)
  const isPaused = status && DOC_STATUS.PAUSED.includes(status as any)
  const isWaiting = status && DOC_STATUS.WAITING.includes(status as any)
  const isCompleted = status && DOC_STATUS.COMPLETED.includes(status as any)
  const isError = status && DOC_STATUS.ERROR.includes(status as any)

  // 获取按钮文案
  const buttonText = isProcessing
    ? t('datasetDocuments.embedding.stop') // 处理中显示"停止"
    : isPaused
      ? t('datasetDocuments.embedding.resume') // 暂停中显示"继续"
      : isWaiting
        ? t('datasetDocuments.embedding.waiting') // 等待中显示"等待中"
        : ''

  // 判断是否显示按钮（完成、错误、加载中或无文案时不显示）
  const shouldShowButton = !isCompleted && !isError && !isLoading && buttonText

  if (!shouldShowButton)
    return null

  return (
    <Button
      variant={'primary'}
      onClick={onSwitch}
      disabled={isManuallyResuming || isLoading || isSwitching || isWaiting}
      loading={isSwitching || isWaiting}
    >
      {buttonText}
    </Button>
  )
}

/**
 * 规则详情组件
 * 显示文档处理规则的详细信息
 */
const RuleDetail: FC<{
  sourceData?: ProcessRuleResponse // 规则数据
  docName?: string // 文档名称
  layout: 'col' | 'grid' // 布局方式：列表或网格
}> = ({ sourceData, docName, layout }) => {
  const { t } = useTranslation()
  const { dataset: currentDataset } = useContext(DatasetDetailContext)

  // 分段规则映射表
  const segmentationRuleMap = {
    docName: t('datasetDocuments.embedding.docName'),
    automatic: t('datasetDocuments.embedding.automatic'),
    mode: t('datasetDocuments.embedding.mode'),
    embeddingModel: t('datasetDocuments.embedding.embeddingModel'),
  }

  /**
   * 获取规则名称
   * 将规则ID转换为可读的名称
   */
  const getRuleName = useCallback(
    (key: string) => {
      if (key === 'remove_extra_spaces')
        return t('datasetCreation.stepTwo.removeExtraSpaces')

      if (key === 'remove_urls_emails')
        return t('datasetCreation.stepTwo.removeUrlEmails')

      if (key === 'remove_stopwords')
        return t('datasetCreation.stepTwo.removeStopwords')
    },
    [t],
  )

  /**
   * 获取规则值
   * 根据字段类型返回对应的值
   */
  const getValue = useCallback(
    (field: string) => {
      let value: string | number | undefined = '-'
      switch (field) {
        case 'docName':
          value = docName
          break
        case 'mode':
          value
            = sourceData?.mode === 'automatic'
              ? (t('datasetDocuments.embedding.auto') as string)
              : (t('datasetDocuments.embedding.custom') as string)
          break
        case 'segmentLength':
          value = sourceData?.rules?.segmentation?.max_tokens
          break
        case 'embeddingModel':
          value = currentDataset?.embedding_model
          break
        default:
          value
            = sourceData?.mode === 'automatic'
              ? (t('datasetDocuments.embedding.auto') as string)
              : sourceData?.rules?.pre_processing_rules
                ?.map((rule: PreProcessingRule) => {
                  return rule.enabled ? getRuleName(rule.id) : undefined
                })
                .filter(Boolean)
                .join(';')
          break
      }
      return value
    },
    [docName, sourceData, t, getRuleName, currentDataset],
  )

  // 列表布局
  if (layout === 'col') {
    return (
      <div className="flex flex-col first:mt-0">
        {Object.keys(segmentationRuleMap).map((field) => {
          return (
            <FieldInfo
              key={field}
              label={
                segmentationRuleMap[field as keyof typeof segmentationRuleMap]
              }
              displayedValue={String(getValue(field))}
            />
          )
        })}
      </div>
    )
  }

  // 网格布局
  if (layout === 'grid') {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 ">
        {Object.keys(segmentationRuleMap).map((field) => {
          return (
            <FieldInfo
              key={field}
              label={
                segmentationRuleMap[field as keyof typeof segmentationRuleMap]
              }
              displayedValue={String(getValue(field))}
            />
          )
        })}
      </div>
    )
  }
}

/**
 * 文档嵌入详情组件
 * 显示文档处理状态、进度和操作按钮
 */
const EmbeddingDetail: FC<Props> = ({
  detail,
  onSeeDocumentInfo,
  datasetId: dstId,
  documentId: docId,
  detailUpdate,
}) => {
  const { t } = useTranslation()
  const { notify } = useContext(ToastContext)
  const { datasetId = '', documentId = '' } = useContext(DocumentContext)
  const localDatasetId = dstId ?? datasetId
  const localDocumentId = docId ?? documentId

  // 状态管理
  const isStopQuery = useRef(false) // 是否停止查询的标记
  const isManuallyResuming = useRef(false) // 是否手动恢复的标记
  const [isSwitching, setIsSwitching] = useState(false) // 是否正在切换状态
  const [indexingStatusDetail, setIndexingStatusDetail] = useState<IndexingStatusResponse>() // 索引状态详情
  const isLoading = indexingStatusDetail === undefined // 是否正在加载

  // 获取文档处理规则
  const { data: ruleDetail } = useSWR(
    {
      action: 'fetchProcessRule',
      params: { documentId: localDocumentId },
    },
    apiParams => fetchProcessRule(omit(apiParams, 'action')),
    {
      revalidateOnFocus: false,
    },
  )

  // 状态判断
  const currentStatus = indexingStatusDetail?.indexing_status as DocStatus | undefined
  const isProcessing = currentStatus && DOC_STATUS.PROCESSING.includes(currentStatus as any)
  const isPaused = currentStatus && DOC_STATUS.PAUSED.includes(currentStatus as any)
  const isWaiting = currentStatus && DOC_STATUS.WAITING.includes(currentStatus as any)
  const isCompleted = currentStatus && DOC_STATUS.COMPLETED.includes(currentStatus as any)
  const isError = currentStatus && DOC_STATUS.ERROR.includes(currentStatus as any)

  /**
   * 获取文档索引状态
   * 如果不是手动恢复状态，则更新状态详情
   */
  const fetchIndexingStatus = useCallback(async () => {
    const status = await doFetchIndexingStatus({
      datasetId: localDatasetId,
      documentId: localDocumentId,
    })
    if (!isManuallyResuming.current)
      setIndexingStatusDetail(status)
    return status
  }, [localDatasetId, localDocumentId])

  /**
   * 停止查询文档状态
   * 设置停止查询标记为true
   */
  const stopQueryStatus = useCallback(() => {
    isStopQuery.current = true
  }, [])

  /**
   * 开始查询文档状态
   * 定期轮询文档状态，根据不同状态设置不同的轮询间隔
   */
  const startQueryStatus = useCallback(async () => {
    if (isStopQuery.current)
      return
    const queryStatus = async () => {
      try {
        const indexingStatusDetail = await fetchIndexingStatus()
        const status = indexingStatusDetail.indexing_status

        // 根据不同状态设置不同的轮询间隔
        let nextQueryDelay = 2500
        if (DOC_STATUS.WAITING.includes(status as any))
          nextQueryDelay = 1000 // waiting/queuing状态下更频繁查询
        else if (DOC_STATUS.PROCESSING.includes(status as any))
          nextQueryDelay = 1500 // processing状态下适中频率

        // 判断是否处理结束
        if (judgeDocIndexEnd({
          index_status: 'completed',
          indexing_status: status,
        })) {
          stopQueryStatus()
          return
        }

        await sleep(nextQueryDelay)
        await startQueryStatus()
      }
      catch (e) {
        await sleep(1500) // 错误时使用较短的重试间隔
        await startQueryStatus()
      }
    }
    detailUpdate()
    queryStatus()
  }, [detailUpdate, fetchIndexingStatus, stopQueryStatus])

  // 组件挂载时开始查询状态，卸载时停止查询
  useEffect(() => {
    isStopQuery.current = false
    startQueryStatus()
    return () => {
      stopQueryStatus()
    }
  }, [startQueryStatus, stopQueryStatus])

  /**
   * 处理状态切换（停止/继续）
   * 发送请求并更新本地状态
   */
  const handleSwitch = async () => {
    setIsSwitching(true)
    const opApi = isProcessing ? pauseDocIndexing : resumeDocIndexing
    const [e] = await asyncRunSafe<CommonResponse>(
      opApi({
        datasetId: localDatasetId,
        documentId: localDocumentId,
      }) as Promise<CommonResponse>,
    )
    if (!e) {
      notify({
        type: 'success',
        message: t('common.actionMsg.modifiedSuccessfully'),
      })

      // 立即更新状态
      const newStatus = isProcessing ? 'paused' : 'indexing'
      setIndexingStatusDetail(prev => prev
        ? {
          ...prev,
          indexing_status: newStatus,
        }
        : undefined)

      // 如果是继续操作，需要重新开始查询
      if (!isProcessing) {
        isManuallyResuming.current = true
        await sleep(1000) // 减少等待时间
        isStopQuery.current = false
        isManuallyResuming.current = false
        startQueryStatus() // 立即开始新的查询
      }
    }
    setIsSwitching(false)
  }

  return (
    <>
      {/* 头部及操作按钮  */}
      <div className={cn(datasetStyle['left-title'], 'justify-between flex')}>
        <div className="flex-1">
          {isProcessing && t('datasetDocuments.embedding.processing')}
          {isCompleted && t('datasetDocuments.embedding.completed')}
          {isPaused && t('datasetDocuments.embedding.paused')}
          {isError && t('datasetDocuments.embedding.error')}
          {isWaiting && t('datasetDocuments.embedding.waiting')}
          {isLoading && t('datasetDocuments.embedding.loading')}
        </div>
        <div className="flex-none">
          <StatusSwitchButton
            status={currentStatus}
            isLoading={isLoading}
            isSwitching={isSwitching}
            isManuallyResuming={isManuallyResuming.current}
            onSwitch={handleSwitch}
          />
        </div>
      </div>
      {/* 内容区域 */}
      <div className={datasetStyle['left-content']}>
        {/* 进度条 */}
        <Progress
          showInfo={false}
          percent={indexingStatusDetail?.doc_process}
          className="!leading-[8px]"
        />
        {/* 进度数据 */}
        <div className={s.progressData}>
          <div>
            {t('datasetDocuments.embedding.segments')}{' '}
            {indexingStatusDetail?.completed_segments}/
            {indexingStatusDetail?.total_segments} · {indexingStatusDetail?.doc_process}%
          </div>
        </div>
        {/* 规则详情 */}
        <RuleDetail
          sourceData={ruleDetail}
          docName={detail?.name}
          layout="grid"
        />
        {/* 预览区域 */}
        <>
          <Divider className="!mt-6 !mb-8" />
          <div className={s.previewTip}>
            {t('datasetDocuments.embedding.previewTip')}
          </div>
          <div className={s.cardWrapper}>
            {[1, 2, 3, 4].map((v, index) => (
              <SegmentCard
                key={index}
                loading={true}
                detail={{ position: v } as any}
              />
            ))}
          </div>
        </>
        {/* 查看文件按钮 */}
        <Button
          className="w-[110px]"
          variant="info"
          size="large"
          onClick={onSeeDocumentInfo}
        >
          {t('datasetDocuments.embedding.files')}
          <ArrowDown className="w-4 h-4" />
        </Button>
      </div>
    </>
  )
}

export default React.memo(EmbeddingDetail)
