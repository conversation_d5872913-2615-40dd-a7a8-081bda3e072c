'use client'
import { Config<PERSON>rovider, Tabs } from 'antd'
import {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useRouter } from 'next/navigation'
import { useMount } from 'ahooks'
import s from './styles/index.module.css'
import type { AppListResponse } from '@/models/app'
import cn from '@/utils/classnames'
import { GRAY } from '@/themes/var-define'
import { AppMarketFeature, AppMarketSort } from '@/types/app-market'
/* 公共组件 */
import SearchInput from '@/app/components/base/input/search-input'
// import AppSquareCard from '@/app/components/app-square/card'
import ToolSquareCard from '@/app/components/tool-square/card'
import IntersectScrollbar from '@/app/components/base/scrollbar/intersect-scrollbar'
import Empty from '@/app/components/base/empty'
import Select from '@/app/components/base/select/new-index'
import { APP_NAME } from '@/config'
import { fetchToolMarket, fetchToolMarketCategories } from '@/service/tools'
import type { Collection } from '@/types/tools'
import Loading from '@/app/components/base/loading'
import type { ScrollbarRef } from '@/app/components/base/scrollbar'
import { useAppContext } from '@/context/app-context'
// 生成获取工具广场列表接口参数
const getKey = (
  pageIndex: number,
  activeTab: string,
  sort: AppMarketSort,
  keywords: string,
) => {
  const params: any = {
    page: pageIndex,
    limit: 30,
    name: keywords,
    sort_by: sort,
    featured: AppMarketFeature.Normal,
  }

  if (activeTab !== 'all')
    params.category_id = activeTab
  else delete params.category_id
  return params
}

const ToolSquare = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { canViewApp } = useAppContext()

  const scrollbarRef = useRef<ScrollbarRef>(null)
  // 当前激活tab
  const [activeTab, setActiveTab] = useState('all')
  // 排序方式
  const [sort, setSort] = useState<AppMarketSort>(AppMarketSort.NEW)
  // 关键字
  const [keywords, setKeywords] = useState('')
  // 分页信息
  const [pagination, setPagination] = useState({
    page: 1,
    total: 0,
  })
  // 是否正在加载
  const [loading, setLoading] = useState(true)
  // 卡片列表
  const [cardList, setCardList] = useState<Array<Collection>>([])
  // 工具页面tab列表
  const [tabList, setTabList] = useState([
    {
      key: 'all',
      label: t('app.types.all'),
    },
  ])
  // 排序列表
  const sortList = [
    {
      label: t('app.sort.new'),
      value: AppMarketSort.NEW,
    },
    {
      label: t('app.sort.hot'),
      value: AppMarketSort.HOT,
    },
  ]

  // 是否还有未加载的部分
  const hasMore = useMemo(() => {
    return pagination.total > pagination.page * 30
  }, [pagination])

  // 获取工具分类
  const getToolMarketCategories = async () => {
    // 获取工具市场分组tab
    try {
      const res = await fetchToolMarketCategories()
      setTabList([
        ...tabList,
        ...res.map((item: { id: any; name: any }) => ({
          key: item.id,
          label: item.name,
        })),
      ])
      // 处理 res
    }
    catch (error) {
      console.error('获取分类数据失败:', error)
      // 可选：给用户提示或做其他兜底处理
    }
  }
  // 获取工具列表接口
  const fetchToolSquareCard = useCallback(
    async (type: 'normal' | 'clear' = 'normal', params?: any) => {
      const param
        = params || getKey(pagination.page, activeTab, sort, keywords)
      setLoading(true)
      await (fetchToolMarket(param) as unknown as Promise<AppListResponse>)
        .then((res) => {
          console.log('获取工具列表数据成功:', res)
          setCardList(
            type === 'normal'
              ? [...cardList, ...(res.data as unknown as Collection[])]
              : [...(res.data as unknown as Collection[])],
          )
          setPagination({
            page: param.page,
            total: res.total,
          })
        })
        .finally(() => {
          setLoading(false)
        })
    },
    [activeTab, cardList, keywords, pagination.page, sort],
  )
  // 重置工具列表
  const resetToolSquareCard = useCallback(
    (tab = activeTab, sorting = sort, name = keywords) => {
      const param = getKey(1, tab, sorting, name)
      setPagination({
        page: 1,
        total: 0,
      })
      fetchToolSquareCard('clear', param)
    },
    [activeTab, fetchToolSquareCard, keywords, sort],
  )
  // 获取下一页
  const nextToolSquareCard = async () => {
    const container = scrollbarRef.current?.getContainer()
    if (container) {
      const { scrollTop, clientHeight, scrollHeight } = container
      if (scrollHeight - 100 <= scrollTop + clientHeight) {
        if (hasMore) {
          setPagination({
            page: pagination.page + 1,
            total: pagination.total,
          })
          await fetchToolSquareCard(
            'normal',
            getKey(pagination.page + 1, activeTab, sort, keywords),
          )
        }
      }
    }
  }

  // 变更标签
  const changeSort = (value: AppMarketSort) => {
    setSort(value)
    resetToolSquareCard(activeTab, value, keywords)
  }
  // 变更关键字方法
  const changeKeyWord = (value: string) => {
    setKeywords(value)
    resetToolSquareCard(activeTab, sort, value)
  }
  // 变更tab方法
  const changeTab = (value: string) => {
    setActiveTab(value)
    resetToolSquareCard(value, sort, keywords)
  }

  useMount(() => {
    resetToolSquareCard()
    getToolMarketCategories()
  })

   useEffect(() => {
    if (typeof window !== 'undefined')
      document.title = `${t('menus.toolMarket')} - ${APP_NAME}`
    if (!canViewApp)
      return router.replace('/square')
  }, [t, router])

  // 头部信息node
  const HeaderNode = (
    <div className={s['app-square-list-header']}>
      <span className={s['header-title']}>{t('toolMarket.title')}</span>
    </div>
  )
  // 筛选node
  const FilterNode = (
    <div className={s['app-square-list-filter']}>
      <Tabs
        className={s['search-tab']}
        activeKey={activeTab}
        items={tabList}
        onChange={changeTab}
      ></Tabs>
      <div className={s['search-criteria']}>
        <ConfigProvider
          theme={{
            components: {
              Select: {
                colorBgContainer: 'transparent',
                colorBorder: GRAY['G3-5'],
              },
              Input: {
                colorBgContainer: 'transparent',
                colorBorder: GRAY['G3-5'],
              },
            },
          }}
        >
          <Select
            value={sort}
            onChange={changeSort}
            options={sortList}
            className="mr-3 w-[110px] h-[36px]"
          ></Select>
          <SearchInput
            placeholder={t('app.placeholder.defaultInput') as string}
            value={keywords}
            className="w-[220px]"
            onChange={changeKeyWord}
          />
        </ConfigProvider>
      </div>
    </div>
  )
  return (
    <IntersectScrollbar
      ref={scrollbarRef}
      fixClass={s['fix-content']}
      target={'app-square-list-banner'}
      onScrollY={nextToolSquareCard}
      fixContent={
        <>
          {/* 头部信息 */}
          {HeaderNode}
          {/* 筛选条件 */}
          {FilterNode}
        </>
      }
    >
      {/* 头部信息 */}
      {HeaderNode}
      {/* 固定部分 */}
      <div id='app-square-list-banner'></div>
      {/* 筛选条件 */}
      {FilterNode}
      {/* 卡片列表 */}
      <div
        className={cn(s['base-card-list'], cardList.length && s['card-list'])}
      >
        {cardList.length
          ? (
            cardList?.map(collection => (
              <ToolSquareCard key={collection.id} collection={collection} />
            ))
          )
          : (
            <Empty text={t('tools.notify.noTool')}></Empty>
          )}
      </div>
      {loading && <div className='sticky h-[calc(100%-151px)] w-full bottom-0'>
        <Loading type='area'></Loading>
      </div>}
    </IntersectScrollbar>
  )
}

export default ToolSquare
