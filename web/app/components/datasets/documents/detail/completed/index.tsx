'use client'
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { useContext } from 'use-context-selector'
import { isNil, omitBy } from 'lodash-es'
import { Pagination } from 'antd'
import { DocumentContext } from '../index'
import SegmentCard from '../segment-card'
import { ProcessStatus } from '../batch-modal'
import BatchImportResult from '../batch-modal/batch-import-result'
import {
  deleteSegment,
  disableSegment,
  enableSegment,
  fetchSegments,
  fetchSegmentsIndexingStatus,
  updateSegment,
} from '@/service/datasets'
import type {
  SegmentDetailModel,
  SegmentUpdater,
  SegmentsIndexingStatusResponse,
  SegmentsQuery,
  SegmentsResponse,
} from '@/models/datasets'
import type { CommonResponse } from '@/models/common'

// 公共组件寄能力
import style from '@/app/components/datasets/styles/style.module.css'
import SegmentDetail from '@/app/components/datasets/common/segment-detail'
import SegmentDetailStructured from '@/app/components/datasets/common/segment-detail/segment-detail-structured'
import { judgeIsIndexingSeg } from '@/app/components/datasets/utils'
import cn from '@/utils/classnames'
import { asyncRunSafe } from '@/utils'
import { ToastContext } from '@/app/components/base/toast'
import SearchInput from '@/app/components/base/input/search-input'
import CardList from '@/app/components/base/card-list'
import Select from '@/app/components/base/select/new-index'
import Button from '@/app/components/base/button'
import { ArrowDown } from '@/app/components/base/icons/src/vender/line/arrows'
import Empty from '@/app/components/base/empty'
import { useProviderContext } from '@/context/provider-context'
import { formatNumber } from '@/utils/format'
import { DatesetFileTypeCode } from '@/models/datasets'

type ICompletedProps = {
  embeddingAvailable: boolean
  onClearImportStatus: () => void
  onSeeDocumentInfo: () => void
  importStatus: ProcessStatus | undefined | string
  archived?: boolean
  category?: string | number | undefined
  // data: Array<{}> // all/part segments
}
// 分段详情列表
const Completed = forwardRef(
  (
    {
      embeddingAvailable,
      onClearImportStatus,
      onSeeDocumentInfo,
      importStatus,
      archived,
      category,
    }: ICompletedProps,
    ref,
  ) => {
    const { t } = useTranslation()
    const { notify } = useContext(ToastContext)
    const {
      datasetId = '',
      documentId = '',
      docForm,
    } = useContext(DocumentContext)
    // 当前分段信息
    const [currSegment, setCurrSegment] = useState<{
      segInfo?: SegmentDetailModel
      showModal: boolean
      showModalStructured: boolean
    }>({ showModal: false, showModalStructured: false })
    // 搜索条件——关键字
    const [searchValue, setSearchValue] = useState<string>() // the search value
    // 搜索条件——状态
    const [selectedStatus, setSelectedStatus] = useState<boolean | 'all'>(
      'all',
    ) // the selected status, enabled/disabled/undefined
    // 页大小
    const pageSize = useRef(12)

    // 全部分段结果
    const [allSegments, setAllSegments] = useState<SegmentDetailModel[]>([]) // all segments data
    // 分段总数
    const [total, setTotal] = useState<number | undefined>()
    // 当前页码
    const [currentPage, setCurrentPage] = useState(1)
    // 是否正在加载
    const [loading, setLoading] = useState(false)
    // 正在索引中的分段
    const [indexingSegments, setIndexingSegments] = useState<Array<string>>([])
    // 是否是私有知识库
    const { useXIYANRag } = useProviderContext()
    // 获取分段数据
    const getSegments = useCallback(async () => {
      setLoading(true)
      const [e, res] = await asyncRunSafe<SegmentsResponse>(
        fetchSegments({
          datasetId,
          documentId,
          params: omitBy(
            {
              limit: pageSize.current,
              page: currentPage,
              keyword: searchValue,
              enabled: selectedStatus === 'all' ? 'all' : !!selectedStatus,
            },
            isNil,
          ) as SegmentsQuery,
        }) as Promise<SegmentsResponse>,
      )
      if (!e) {
        setAllSegments([...(res.data || [])])
        setIndexingSegments([
          ...res.data
            .filter(item => judgeIsIndexingSeg(item))
            .map(item => item.id),
        ])
        setTotal(res?.total || 0)
      }
      setLoading(false)
    }, [currentPage, datasetId, documentId, searchValue, selectedStatus])
    // 重新获取分段数据
    const resetList = useCallback(() => {
      setIndexingSegments([])
      setAllSegments([])
      setLoading(false)
      setTotal(undefined)
      setCurrentPage(1)
      getSegments()
    }, [getSegments])
    // 点击分段卡片
    const onClickCard = (detail: SegmentDetailModel) => {
      // 非结构化分段
      if (category === DatesetFileTypeCode.Unstructured)
        setCurrSegment({ segInfo: detail, showModal: true, showModalStructured: false })
      // 结构化分段
      else
        setCurrSegment({ segInfo: detail, showModal: false, showModalStructured: true })
    }

    // 关闭调整分段卡片弹窗
    const onCloseModal = () => {
      setCurrSegment({ ...currSegment, showModal: false, showModalStructured: false })
    }
    // 变更卡片是否启用
    const onChangeSwitch = async (segId: string, enabled: boolean) => {
      const opApi = enabled ? enableSegment : disableSegment
      // 更新索引状态
      setIndexingSegments([...indexingSegments, segId])
      for (const seg of allSegments) {
        if (seg.id === segId) {
          seg.index_status = 'indexing'
          setCurrSegment({
            segInfo: Object.assign({}, seg),
            showModal: currSegment.showModal,
            showModalStructured: currSegment.showModalStructured,
          })
        }
      }
      setAllSegments([...allSegments])

      try {
        // 等待启用/禁用操作完成
        const [e] = await asyncRunSafe<CommonResponse>(
          opApi({ datasetId, segmentId: segId }) as Promise<CommonResponse>,
        )
        if (e)
          throw e

        // 等待索引状态检查完成
        const [statusErr, statusRes] = await asyncRunSafe<SegmentsIndexingStatusResponse>(
          fetchSegmentsIndexingStatus({
            datasetId,
            documentId,
            segment_id_list: [segId],
          }) as Promise<SegmentsIndexingStatusResponse>,
        )
        if (statusErr)
          throw statusErr

        // 只有当两个操作都成功完成时，才更新状态
        notify({
          type: 'success',
          message: t('common.actionMsg.modifiedSuccessfully'),
        })

        const segStatus = statusRes.segement_status[segId]
        for (const seg of allSegments) {
          if (seg.id === segId) {
            seg.enabled = enabled
            seg.index_status = segStatus
            setCurrSegment({
              segInfo: Object.assign({}, seg),
              showModal: currSegment.showModal,
              showModalStructured: currSegment.showModalStructured,
            })
          }
        }
        setAllSegments([...allSegments])

        // 更新索引状态列表
        if (segStatus === 'completed')
          setIndexingSegments(indexingSegments.filter(id => id !== segId))
      }
      catch (error) {
        notify({
          type: 'error',
          message: t('common.actionMsg.modificationFailed'),
        })
        // 恢复原始状态
        for (const seg of allSegments) {
          if (seg.id === segId) {
            seg.index_status = 'completed'
            setCurrSegment({
              segInfo: Object.assign({}, seg),
              showModal: currSegment.showModal,
              showModalStructured: currSegment.showModalStructured,
            })
          }
        }
        setAllSegments([...allSegments])
        setIndexingSegments(indexingSegments.filter(id => id !== segId))
      }
    }
    // 删除分段卡片
    const onDelete = async (segId: string) => {
      const [e] = await asyncRunSafe<CommonResponse>(
        deleteSegment({
          datasetId,
          documentId,
          segmentId: segId,
        }) as Promise<CommonResponse>,
      )
      if (!e) {
        notify({
          type: 'success',
          message: t('common.actionMsg.modifiedSuccessfully'),
        })
        resetList()
      }
    }
    // 更新分段卡片
    const handleUpdateSegment = async (
      segmentId: string,
      question: string,
      answer: string,
      keywords: string[],
      headers?: string[],
      values?: string[],
    ) => {
      const params: SegmentUpdater = { content: '' }
      if (docForm === 'qa_model') {
        if (!question.trim()) {
          return notify({
            type: 'error',
            message: t('datasetDocuments.segment.questionEmpty'),
          })
        }
        if (!answer.trim()) {
          return notify({
            type: 'error',
            message: t('datasetDocuments.segment.answerEmpty'),
          })
        }

        params.content = question
        params.answer = answer
      }
      else {
        if (!question.trim()) {
          return notify({
            type: 'error',
            message: t('datasetDocuments.segment.contentEmpty'),
          })
        }

        params.content = question
      }
      if (keywords.length)
        params.keywords = keywords
      if (category !== DatesetFileTypeCode.Unstructured) {
        delete params.content
        params.headers = headers
        params.values = values
      }
      try {
        const res = await updateSegment({
          datasetId,
          documentId,
          segmentId,
          body: params,
        })
        notify({
          type: 'success',
          message: t('common.actionMsg.modifiedSuccessfully'),
        })
        onCloseModal()
        // for (const seg of allSegments) {
        //   if (seg.id === segmentId) {
        //     seg.answer = res.data.answer
        //     seg.content = res.data.content
        //     seg.keywords = res.data.keywords
        //     seg.word_count = res.data.word_count
        //     seg.hit_count = res.data.hit_count
        //     seg.index_node_hash = res.data.index_node_hash
        //     seg.enabled = res.data.enabled
        //   }
        // }
        // setAllSegments([...allSegments])
        // 重新获取最新的分段数据
        getSegments()
      }
      catch {}
    }
    // 变更筛选体状态
    const onChangeStatus = (value: String) => {
      setCurrentPage(1)
      setSelectedStatus(value === 'all' ? 'all' : !!value)
    }
    // 变更查询体哦阿健
    const onChangeKeyword = (value: string) => {
      setCurrentPage(1)
      setSearchValue(value)
    }

    // 批量获取索引状态
    const batchGetIndexingStatus = useCallback(async () => {
      if (indexingSegments.length) {
        const [e, res] = await asyncRunSafe<SegmentsIndexingStatusResponse>(
          fetchSegmentsIndexingStatus({
            datasetId,
            documentId,
            segment_id_list: indexingSegments,
          }) as Promise<SegmentsIndexingStatusResponse>,
        )
        if (!e) {
          // 获取到已经索引完成的分段
          const completedSeg = Object.entries(res.segement_status)
            .filter(([, value]) => value === 'completed')
            .map(([key]) => key)
          // 更新分段的信息
          for (const seg of allSegments) {
            if (completedSeg.includes(seg.id))
              seg.index_status = 'completed'
            if (
              currSegment.segInfo
              && completedSeg.includes(currSegment.segInfo?.id)
            ) {
              setCurrSegment({
                segInfo: {
                  ...currSegment.segInfo,
                  index_status: 'completed',
                },
                showModal: currSegment.showModal,
                showModalStructured: currSegment.showModalStructured,
              })
            }
          }
          setAllSegments([...allSegments])
          // 变更还要索引的分段列表
          setIndexingSegments(
            Object.entries(res.segement_status)
              .filter(([, value]) => value !== 'completed')
              .map(([key]) => key),
          )
        }
      }
    }, [allSegments, currSegment, datasetId, documentId, indexingSegments])

    // 正在索引的
    useEffect(() => {
      const intervalController = setInterval(() => {
        batchGetIndexingStatus()
      }, 1000)
      return () => clearInterval(intervalController)
    }, [batchGetIndexingStatus])
    // 导入状态变化
    useEffect(() => {
      if (importStatus === ProcessStatus.COMPLETED) {
        notify({
          type: 'success',
          message: t('datasetDocuments.list.batchModal.importSuccess'),
        })
        resetList()
      }
    }, [importStatus])
    useEffect(() => {
      getSegments()
    }, [getSegments])

    useImperativeHandle(ref, () => ({
      reset() {
        return resetList()
      },
    }))

    return (
      <>
        {/* 顶栏-标题及操作 */}
        <div className={cn(style['left-title'], 'justify-between')}>
          {/* 批量导入状态 */}
          {importStatus
            ? (
              !useXIYANRag && <BatchImportResult
                importStatus={importStatus as any}
                onClear={onClearImportStatus}
              ></BatchImportResult>
            )
            : (
              <span className="text-gray-G2 text-S3 leading-H3">
                {total
                  ? t('datasetDocuments.segment.paragraphs', {
                    number: formatNumber(total),
                  })
                  : '--'}
              </span>
            )}
          <div className="flex items-center gap-2">
            <Select
              onChange={onChangeStatus}
              options={[
                { value: 'all', label: t('common.info.all') },
                { value: 0, label: t('datasetDocuments.list.status.disabled') },
                { value: 1, label: t('datasetDocuments.list.status.enabled') },
              ]}
              defaultValue="all"
              className="w-[120px]"
            ></Select>
            <SearchInput className="w-[360px]" onChange={onChangeKeyword} />
          </div>
        </div>
        {/* 滚动列表 */}
        <div
          className={cn(style['left-content'], '!h-[calc(100%-52px)] !px-0')}
        >
          {allSegments.length
            ? (
              <CardList
                wrapperClassName="!h-auto !grow-0 !min-h-[250px]"
                className="!px-8"
                type="normal"
                layout="col"
                loading={loading}
              >
                {allSegments.map(seg => (
                  <SegmentCard
                    key={seg.id}
                    detail={seg}
                    loading={false}
                    onClick={() => onClickCard(seg)}
                    onChangeSwitch={onChangeSwitch}
                    onDelete={onDelete}
                    archived={archived}
                    embeddingAvailable={embeddingAvailable}
                    category={category}
                  ></SegmentCard>
                ))}
              </CardList>
            )
            : (
              <Empty></Empty>
            )}
          {/* 底部栏 */}
          <div className="relative mt-6 px-8 h-9 shrink-0">
            <Button
              className="absolute"
              variant="info"
              size="large"
              onClick={onSeeDocumentInfo}
            >
              {t('datasetDocuments.list.action.data')}
              <ArrowDown className="w-4 h-4" />
            </Button>
            <Pagination
              align="center"
              current={currentPage}
              hideOnSinglePage
              total={total}
              pageSize={pageSize.current}
              onChange={setCurrentPage}
              showQuickJumper={false}
              showSizeChanger={false}
            ></Pagination>
          </div>
        </div>

        {/* 分段详情弹窗 */}
        {currSegment.showModal && (
          <SegmentDetail
            canEdit={embeddingAvailable && !archived}
            segInfo={currSegment.segInfo ?? { id: '' }}
            onUpdate={handleUpdateSegment}
            onCancel={onCloseModal}
            category={category}
          />
        )}
        {currSegment.showModalStructured && (
          <SegmentDetailStructured
            canEdit={embeddingAvailable && !archived}
            isShow={currSegment.showModalStructured}
            segInfo={currSegment.segInfo ?? { id: '' }}
            onUpdate={handleUpdateSegment}
            onCancel={onCloseModal}
          />
        )}
      </>
    )
  },
)
Completed.displayName = 'Completed'

export default Completed
