'use client'
import type { FC } from 'react'
import React, { useCallback, useState } from 'react'
import { RiEqualizer2Line } from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import type { MultipleRetrievalConfig, SingleRetrievalConfig } from '../types'
import { RETRIEVE_TYPE } from '@/types/datasets'
import { DATASET_DEFAULT } from '@/config'
import type { DatasetConfigs } from '@/models/debug'
import type { DataSet } from '@/models/datasets'
import type { ModelConfig } from '@/app/components/workflow/types'

// 知识库公共能力
import ConfigRetrievalContent from '@/app/components/datasets/common/add-dataset-panel/params-config/config-content'
// 账户设置公共能力
import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
// 引入设置弹窗组件
import SettingsModal from '@/app/components/datasets/common/add-dataset-panel/setting-modal'
// G公共能力
import cn from '@/utils/classnames'
import {
  PortalToFollowElem,
  PortalToFollowElemContent,
  PortalToFollowElemTrigger,
} from '@/app/components/base/portal-to-follow-elem'
import Button from '@/app/components/base/button'

type Props = {
  readonly?: boolean
  openFromProps?: boolean
  onOpenFromPropsChange?: (openFromProps: boolean) => void
  // 检索配置
  payload: {
    retrieval_mode: RETRIEVE_TYPE
    multiple_retrieval_config?: MultipleRetrievalConfig
    single_retrieval_config?: SingleRetrievalConfig
  }
  // 模型配置
  singleRetrievalModelConfig?: ModelConfig
  // 选择的知识库
  selectedDatasets: DataSet[]
  // 检索配置变化
  onMultipleRetrievalConfigChange: (config: MultipleRetrievalConfig) => void
  // 检索模式变化
  onRetrievalModeChange: (mode: RETRIEVE_TYPE) => void
  // 模型变化
  onSingleRetrievalModelChange?: (config: ModelConfig) => void
  onSingleRetrievalModelParamsChange?: (config: ModelConfig) => void
  // 控制使用哪个组件
  useSettingsModal?: boolean
}

const RetrievalConfig: FC<Props> = ({
  payload,
  onRetrievalModeChange,
  onMultipleRetrievalConfigChange,
  singleRetrievalModelConfig,

  readonly,
  openFromProps,
  onOpenFromPropsChange,
  selectedDatasets,
  onSingleRetrievalModelChange,
  onSingleRetrievalModelParamsChange,
  useSettingsModal = true,
}) => {
  const { t } = useTranslation()
  const { defaultModel: rerankDefaultModel }
    = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  const { multiple_retrieval_config } = payload

  // 弹窗打开状态
  const [open, setOpen] = useState(false)
  // 是否打开
  const mergedOpen = openFromProps !== undefined ? openFromProps : open

  // 变更弹窗打开
  const handleOpen = useCallback(
    (newOpen: boolean) => {
      setOpen(newOpen)
      onOpenFromPropsChange?.(newOpen)
    },
    [onOpenFromPropsChange],
  )
  // 变更知识库配置
  const handleChange = useCallback(
    (configs: DatasetConfigs, isRetrievalModeChange?: boolean) => {
      if (isRetrievalModeChange) {
        onRetrievalModeChange(configs.retrieval_model)
        return
      }
      onMultipleRetrievalConfigChange({
        top_k: configs.top_k,
        score_threshold: configs.score_threshold_enabled
          ? configs.score_threshold ?? DATASET_DEFAULT.score_threshold
          : 0,
        reranking_model:
          payload.retrieval_mode === RETRIEVE_TYPE.oneWay
            ? undefined
            : !configs.reranking_model?.reranking_provider_name
              ? {
                provider: rerankDefaultModel?.provider?.provider || '',
                model: rerankDefaultModel?.model || '',
              }
              : {
                provider: configs.reranking_model?.reranking_provider_name,
                model: configs.reranking_model?.reranking_model_name,
              },
        reranking_mode: configs.reranking_mode,
        weights: configs.weights as any,
        reranking_enable: configs.reranking_enable,
      })
    },
    [
      onMultipleRetrievalConfigChange,
      payload.retrieval_mode,
      rerankDefaultModel?.provider?.provider,
      rerankDefaultModel?.model,
      onRetrievalModeChange,
    ],
  )

  // 处理SettingsModal保存
  const handleSaveSettings = useCallback(
    (newDataset: DataSet) => {
      // 将SettingsModal的保存结果转换为适合当前组件的格式
      if (newDataset.retrieval_model_dict) {
        const retrievalConfig
          = newDataset.retrieval_model_dict as unknown as MultipleRetrievalConfig
        onMultipleRetrievalConfigChange(retrievalConfig)
      }
      handleOpen(false)
    },
    [onMultipleRetrievalConfigChange, handleOpen],
  )

  // 如果没有选中的数据集，使用默认空数据集
  const currentDataset
    = selectedDatasets.length > 0 ? selectedDatasets[0] : ({} as DataSet)

  return (
    <>
      {!useSettingsModal
        ? (
          <PortalToFollowElem
            open={mergedOpen}
            onOpenChange={handleOpen}
            placement="bottom-end"
            offset={{
              crossAxis: -2,
            }}
          >
            <PortalToFollowElemTrigger
              onClick={() => {
                if (readonly)
                  return
                handleOpen(!mergedOpen)
              }}
            >
              <Button
                variant="ghost"
                size="small"
                disabled={readonly}
                className={cn(open && 'bg-gray-G5')}
              >
                <RiEqualizer2Line className="mr-1 w-3.5 h-3.5" />
                {t('dataset.action.retrievalSettings')}
              </Button>
            </PortalToFollowElemTrigger>
            <PortalToFollowElemContent style={{ zIndex: 1001 }}>
              <div className="w-[404px] pt-3 pb-4 px-4 shadow-xl rounded-2xl border border-gray-200 bg-white">
                <ConfigRetrievalContent
                  datasetConfigs={{
                    retrieval_model: payload.retrieval_mode,
                    reranking_model: multiple_retrieval_config?.reranking_model
                      ?.provider
                      ? {
                        reranking_provider_name:
                          multiple_retrieval_config.reranking_model?.provider,
                        reranking_model_name:
                          multiple_retrieval_config.reranking_model?.model,
                      }
                      : {
                        reranking_provider_name: '',
                        reranking_model_name: '',
                      },
                    top_k:
                    multiple_retrieval_config?.top_k || DATASET_DEFAULT.top_k,
                    score_threshold_enabled: !(
                      multiple_retrieval_config?.score_threshold === undefined
                    || multiple_retrieval_config.score_threshold === null
                    ),
                    score_threshold: multiple_retrieval_config?.score_threshold,
                    datasets: {
                      datasets: [],
                    },
                    reranking_mode: multiple_retrieval_config?.reranking_mode,
                    weights: multiple_retrieval_config?.weights,
                    reranking_enable: multiple_retrieval_config?.reranking_enable,
                  }}
                  onChange={handleChange}
                  isInWorkflow
                  singleRetrievalModelConfig={singleRetrievalModelConfig}
                  onSingleRetrievalModelChange={onSingleRetrievalModelChange}
                  onSingleRetrievalModelParamsChange={
                    onSingleRetrievalModelParamsChange
                  }
                  selectedDatasets={selectedDatasets}
                />
              </div>
            </PortalToFollowElemContent>
          </PortalToFollowElem>
        )
        : (
          <>
            <Button
              variant="ghost"
              size="small"
              disabled={readonly}
              className={cn(mergedOpen && 'bg-gray-G5')}
              onClick={() => {
                if (readonly)
                  return
                handleOpen(!mergedOpen)
              }}
            >
              <RiEqualizer2Line className="mr-1 w-3.5 h-3.5" />
              {t('dataset.action.retrievalSettings')}
            </Button>

            {mergedOpen && (
              <SettingsModal
                isShowSettingsModal={mergedOpen}
                currentDataset={{
                  ...currentDataset,
                  id: currentDataset.id || '',
                  name: currentDataset.name || '',
                  description: currentDataset.description || '',
                  permission: currentDataset.permission || 'only_me',
                  embedding_available: true,
                  indexing_technique:
                  currentDataset.indexing_technique || 'high_quality',
                  retrieval_model_dict:
                  multiple_retrieval_config as unknown as any,
                }}
                onCancel={() => handleOpen(false)}
                onSave={handleSaveSettings}
              />
            )}
          </>
        )}
    </>
  )
}
export default React.memo(RetrievalConfig)
