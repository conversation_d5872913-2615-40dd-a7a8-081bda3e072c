'use client'
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import RetrievalParamConfigXiyan from '../retrieval-param-config-xiyan'
import style from '../styles/style.module.css'
import type { RetrievalConfig } from '@/types/datasets'
import { RETRIEVE_METHOD, WeightedScoreEnum } from '@/types/datasets'
import { useProviderContext } from '@/context/provider-context'

import cn from '@/utils/classnames'
import RadioCard from '@/app/components/base/radio-card'
import { DEFAULT_WEIGHTED_SCORE } from '@/config/dataset'

type Props = {
  value?: RetrievalConfig
  disabled?: boolean
  onChange: (value: RetrievalConfig) => void
}

const RetrievalMethodConfigXiyan = forwardRef(
  ({ value: passValue, disabled = false, onChange }: Props, ref) => {
    const { t } = useTranslation()
    // 支持的向量检索方法
    const { supportRetrievalMethods } = useProviderContext()
    // 当前索引方式
    const [currentRetrievalMethod, setCurrentRetrievalMethod] = useState(
      RETRIEVE_METHOD.hybrid,
    )
    // 混合检索ref
    const hybridRef = useRef<{
      getConfig: () => RetrievalConfig
      resetConfig: () => void
    }>()
    // 全文检索ref
    const fullTextRef = useRef<{
      getConfig: () => RetrievalConfig
      resetConfig: () => void
    }>()
    // 语义检索ref
    const semanticRef = useRef<{
      getConfig: () => RetrievalConfig
      resetConfig: () => void
    }>()
    // 是否使用XIYANRag
    const { useXIYANRag } = useProviderContext()

    // 数据预处理
    const value = useMemo(() => {
      if (passValue) {
        if (!passValue.weights) {
          return {
            ...passValue,
            weights: {
              weight_type: WeightedScoreEnum.Customized,
              vector_setting: {
                vector_weight: DEFAULT_WEIGHTED_SCORE.other.semantic,
                embedding_provider_name: '',
                embedding_model_name: '',
              },
              keyword_setting: {
                keyword_weight: DEFAULT_WEIGHTED_SCORE.other.keyword,
              },
            },
          }
        }
        return passValue
      }
    }, [passValue])

    // 变更索引方法
    const changeRetrievalMethod = (retrivalMethod: RETRIEVE_METHOD) => {
      setCurrentRetrievalMethod(retrivalMethod)
      if (retrivalMethod === RETRIEVE_METHOD.hybrid)
        onChange(hybridRef.current!.getConfig())
      if (retrivalMethod === RETRIEVE_METHOD.semantic)
        onChange(semanticRef.current!.getConfig())
      if (retrivalMethod === RETRIEVE_METHOD.fullText)
        onChange(fullTextRef.current!.getConfig())
    }
    // 重置索引方法
    const resetRetrievalMethod = (retrivalConfig: RetrievalConfig) => {
      if (retrivalConfig.search_method !== RETRIEVE_METHOD.hybrid)
        hybridRef.current?.resetConfig()
      if (retrivalConfig.search_method !== RETRIEVE_METHOD.fullText)
        fullTextRef.current?.resetConfig()
      if (retrivalConfig.search_method !== RETRIEVE_METHOD.semantic)
        semanticRef.current?.resetConfig()
    }

    useEffect(() => {
      if (value)
        setCurrentRetrievalMethod(value.search_method)
    }, [value])
    useEffect(() => {
      if (!passValue) {
        if (supportRetrievalMethods.includes(RETRIEVE_METHOD.hybrid)) {
          setCurrentRetrievalMethod(RETRIEVE_METHOD.hybrid)
          onChange(hybridRef.current?.getConfig() as RetrievalConfig)
        }
        else if (supportRetrievalMethods.includes(RETRIEVE_METHOD.semantic)) {
          setCurrentRetrievalMethod(RETRIEVE_METHOD.semantic)
          onChange(semanticRef.current?.getConfig() as RetrievalConfig)
        }
        else if (supportRetrievalMethods.includes(RETRIEVE_METHOD.fullText)) {
          setCurrentRetrievalMethod(RETRIEVE_METHOD.fullText)
          onChange(fullTextRef.current?.getConfig() as RetrievalConfig)
        }
      }
    }, [])

    useImperativeHandle(ref, () => ({
      resetConfig: resetRetrievalMethod,
    }))

    // 判断是否为混合检索
    const isHybrid = currentRetrievalMethod === RETRIEVE_METHOD.hybrid
    // 判断是否为向量检索
    const isSemantic = currentRetrievalMethod === RETRIEVE_METHOD.semantic
    // 判断是否为全文检索
    const isFulltext = currentRetrievalMethod === RETRIEVE_METHOD.fullText

    return (
      <div className="space-y-2">
        {supportRetrievalMethods.includes(RETRIEVE_METHOD.hybrid) && (
          <RadioCard
            disabled={disabled}
            className={cn(style.radioItem, isHybrid && 'active')}
            title={t('dataset.retrieval.hybrid_search.title')}
            description={t('dataset.retrieval.hybrid_search.description')}
            isChosen={isHybrid}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.hybrid)}
            chosenConfig={
              <RetrievalParamConfigXiyan
                ref={hybridRef}
                type={RETRIEVE_METHOD.hybrid}
                value={value}
                onChange={onChange}
                showConfig={false}
                showTopKItem={false}
              />
            }
          />
        )}
        {supportRetrievalMethods.includes(RETRIEVE_METHOD.fullText) && (
          <RadioCard
            disabled={disabled}
            className={cn(style.radioItem, isFulltext && 'active')}
            title={t('dataset.retrieval.full_text_search.title')}
            description={t('dataset.retrieval.full_text_search.description')}
            isChosen={isFulltext}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.fullText)}
            chosenConfig={
              <RetrievalParamConfigXiyan
                ref={fullTextRef}
                type={RETRIEVE_METHOD.fullText}
                value={value}
                onChange={onChange}
                showConfig={false}
                showTopKItem={false}
              />
            }
          />
        )}
        {supportRetrievalMethods.includes(RETRIEVE_METHOD.semantic) && (
          <RadioCard
            disabled={disabled}
            className={cn(style.radioItem, isSemantic && 'active')}
            title={t('dataset.retrieval.semantic_search.title')}
            description={t('dataset.retrieval.semantic_search.description')}
            isChosen={isSemantic}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.semantic)}
            chosenConfig={
              <RetrievalParamConfigXiyan
                ref={semanticRef}
                type={RETRIEVE_METHOD.semantic}
                value={value}
                onChange={onChange}
                showConfig={false}
                showTopKItem={false}
              />
            }
          />
        )}
        {useXIYANRag && (
          <RadioCard
            // disabled={disabled}
            className={cn(style.radioItem, 'active')}
            title={t('dataset.retrieval.parameter_settings.title')}
            description={''}
            isChosen={true}
            noRadio={true}
            onChosen={() => changeRetrievalMethod(RETRIEVE_METHOD.semantic)}
            chosenConfig={
              <RetrievalParamConfigXiyan
                ref={semanticRef}
                type={RETRIEVE_METHOD.semantic}
                value={value}
                onChange={onChange}
                showConfig={true}
                showTopKItem={true}
                layoutMethod="vertical"
              />
            }
          />
        )}
      </div>
    )
  },
)

RetrievalMethodConfigXiyan.displayName = 'RetrievalMethodConfigXiyan'
export default React.memo(RetrievalMethodConfigXiyan)
