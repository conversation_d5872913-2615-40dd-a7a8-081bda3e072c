import { useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Tabs } from 'antd'
import ProviderAddedCard from './provider-added-card'
import type {
  CustomConfigurationModelFixedFields,
  ModelItem,
  ModelProvider,
} from './declarations'
import {
  ConfigurationMethodEnum,
  CustomConfigurationStatusEnum,
  EventEmitterType,
  ProviderType,
} from './declarations'

import {
  useUpdateModelList,
  useUpdateModelProviders,
} from './hooks'
import s from './styles/index.module.css'
import { isCloudProvider, isLocalProvider, isOfficalProvider, judgeCustomConfigProvider, judgePredefineConfigProvider, removeModel, removeProvider } from './utils'
import { DEFAULT_LOCAL_PROVIDER } from './config'
import ProviderModal from './model-modal/provider-modal'
import cn from '@/utils/classnames'
import { useProviderContext } from '@/context/provider-context'
import { useModalContextSelector } from '@/context/modal-context'
import { useEventEmitterContextContext } from '@/context/event-emitter'
import { useAppContext } from '@/context/app-context'
/* 公共组件 */
import { useToastContext } from '@/app/components/base/toast'
import Confirm from '@/app/components/base/confirm'
import { Add } from '@/app/components/base/icons/src/vender/solid/general'

// 官方模型提供商
type officalModelProviderPageProps = {
  providers: ModelProvider[]
}
const OfficalModelProviderPage = ({ providers }: officalModelProviderPageProps) => {
  const { t } = useTranslation()
  // 是否显示新增模型按钮
  const { canAddOfficialModel } = useAppContext()

  return (
    <>
      <span className={s['provider-group-title']}>{t('account.modelProvider.modalList')}</span>
      <div className='gap-3 flex flex-col'>
        {
          providers?.map(provider => (
            <ProviderAddedCard
              showAdd={canAddOfficialModel && judgeCustomConfigProvider(provider)}
              showTool={canAddOfficialModel && judgePredefineConfigProvider(provider)}
              showItemOperation={canAddOfficialModel}
              key={provider.provider}
              provider={provider}
            />
          ))
        }
      </div>
    </>

  )
}

// 第三方模型提供商
type thirdPartyModelProviderPageProps = {
  cloudProviders: ModelProvider[]
  localProviders: ModelProvider[]
  notConfigedProviders: ModelProvider[]
}
const ThirdPartyModelProviderPage = ({ cloudProviders, localProviders, notConfigedProviders }: thirdPartyModelProviderPageProps) => {
  const { t } = useTranslation()
  const { eventEmitter } = useEventEmitterContextContext()

  // 新增本地provider，
  const addLocalProvider = () => {
    eventEmitter?.emit({
      type: EventEmitterType.setUpModel,
      provider: DEFAULT_LOCAL_PROVIDER,
    } as any)
  }
  // 新增公有云provider
  const addCloudProvider = () => {
    eventEmitter?.emit({
      type: EventEmitterType.addProvider,
    } as any)
  }

  return (
    <>
      {/* 本地模型 */}
      <div className={s['provider-group-title']}>{t('account.modelProvider.modelType.localModel')}</div>
      {
        localProviders.length
          ? (
            <div className='gap-3 flex flex-col'>
              {
                localProviders?.map(provider => (
                  <ProviderAddedCard
                    showTool={judgePredefineConfigProvider(provider)}
                    showAdd={judgeCustomConfigProvider(provider)}
                    key={provider.provider}
                    provider={provider}
                  />
                ))
              }
            </div>
          )
          : <div className={s['add-provider-btn']} onClick={addLocalProvider}>
            <div className={s['add-provider-btn-icon']}>
              <Add></Add>
            </div>
            {t('account.modelProvider.addLocalModal')}
          </div>
      }
      {/* 云模型 */}
      <div className={cn(s['provider-group-title'], 'mt-4')}>{t('account.modelProvider.modelType.cloudModel')}</div>
      {notConfigedProviders.length
        ? <div className={s['add-provider-btn']} onClick={addCloudProvider}>
          <div className={s['add-provider-btn-icon']}>
            <Add></Add>
          </div>
          {t('account.modelProvider.addCloudModal')}
        </div>
        : <></>}
      <div className='gap-3 flex flex-col'>
        {
          cloudProviders?.map(provider => (
            <ProviderAddedCard
              showTool={judgePredefineConfigProvider(provider)}
              showAdd={judgeCustomConfigProvider(provider)}
              key={provider.provider}
              provider={provider}
            />
          ))
        }
      </div>
    </>
  )
}

const ModelProviderPage = () => {
  const { t } = useTranslation()
  const { notify } = useToastContext()
  const { eventEmitter } = useEventEmitterContextContext()
  // 更新模型提供商
  const updateModelProviders = useUpdateModelProviders()
  // 更新模型列表
  const updateModelList = useUpdateModelList()
  // 模型提供商
  const { modelProviders: providers } = useProviderContext()
  // 显示模型配置弹窗
  const setShowModelModal = useModalContextSelector(state => state.setShowModelModal)

  // 当前激活tab
  const [activeTab, setActiveTab] = useState<ProviderType>(ProviderType.offical)
  // 是否显示确认删除
  const [showConfirmDelete, setShowConfirmDelete] = useState(false)
  // 是否显示模型提供商modal
  const [showProviderModal, setShowProviderModal] = useState(false)
  // 要删除的模型信息
  const [delModel, setDelModel] = useState<ModelItem>()
  // 要删除的模型供应商信息
  const [delProvider, setDelProvider] = useState<ModelProvider>()

  const { canViewApp, isSuperUser } = useAppContext()

  // 官方模型提供商, 本地模型提供商，公有云模型，未配置的公有云模型
  const [officalProviders, cloudProviders, localProviders, notConfigedProviders] = useMemo(() => {
    const officalProviders: ModelProvider[] = []
    const cloudProviders: ModelProvider[] = []
    const localProviders: ModelProvider[] = []
    const notConfigedProviders: ModelProvider[] = []

    // 我们暂时将第一个设置为官方，其他的先暂时设置为第三方
    providers.forEach((item) => {
      // 官方模型
      if (isOfficalProvider(item)) {
        officalProviders.push(item)
      }
      else {
        // 是否为激活的
        if ((
          item.custom_configuration.status === CustomConfigurationStatusEnum.active
          || (
            item.system_configuration.enabled === true
            && item.system_configuration.quota_configurations.find(record => record.quota_type === item.system_configuration.current_quota_type)
          )
        )) {
          if (isLocalProvider(item))
            localProviders.push(item)
          else
            cloudProviders.push(item)
        }
        else {
          if (isCloudProvider(item))
            notConfigedProviders.push(item)
        }
      }
    })
    return [officalProviders, cloudProviders, localProviders, notConfigedProviders]
  }, [providers])

  // 模型来源tab列表
  const tabList = useMemo(() => {
    const tabs = [{
      key: ProviderType.offical,
      label: t('account.modelProvider.modelType.officalModel'),
    }]

    if (canViewApp) {
      tabs.push({
        key: ProviderType.thirdParty,
        label: t('account.modelProvider.modelType.thirdPartyModel'),
      })
    }

    return tabs
  }, [canViewApp, t])

  // 更新provider和modal
  const updateModelProviderInfo = (
    provider: ModelProvider,
    CustomConfigurationModelFixedFields?: CustomConfigurationModelFixedFields,
  ) => {
    updateModelProviders()
    // 如果是predefine配置的模型
    if (judgePredefineConfigProvider(provider)) {
      provider.supported_model_types.forEach((type) => {
        updateModelList(type)
      })
    }
    // 如果是custom配置的需要刷新模型列表
    if (judgeCustomConfigProvider(provider) && provider.custom_configuration.status === CustomConfigurationStatusEnum.active) {
      eventEmitter?.emit({
        type: EventEmitterType.updateModelList,
        provider: provider.provider,
      } as any)

      if (CustomConfigurationModelFixedFields?.__model_type)
        updateModelList(CustomConfigurationModelFixedFields?.__model_type)
    }
  }
  // 显示配置model、provider的Modal
  const showModelProviderModal = (
    provider: ModelProvider,
    configType: ConfigurationMethodEnum,
    CustomConfigurationModelFixedFields?: CustomConfigurationModelFixedFields,
  ) => {
    setShowModelModal({
      payload: {
        currentProvider: provider,
        configType,
        currentCustomConfigurationModelFixedFields: CustomConfigurationModelFixedFields,
      },
      onSaveCallback: () => {
        updateModelProviderInfo(
          provider,
          CustomConfigurationModelFixedFields,
        )
      },
    })
  }
  // 删除确认事件
  const onConfirmDelete = async () => {
    let res: any = null
    // 如果存在model和provider都在的情况下，只删除model
    if (delModel && delProvider) {
      res = await removeModel(delProvider.provider, {
        __model_name: delModel!.model,
        __model_type: delModel!.model_type,
      })
    }
    // 否则只删除provider
    else {
      res = await removeProvider(delProvider!.provider)
    }
    setDelModel(undefined)
    setDelProvider(undefined)
    setShowConfirmDelete(false)
    updateModelProviderInfo(delProvider!, delModel
      ? {
        __model_name: delModel.model,
        __model_type: delModel.model_type,
      }
      : undefined)

    if (res.result === 'success')
      notify({ type: 'success', message: t('common.actionMsg.deleteSuccessfully') })
  }

  eventEmitter?.useSubscription((v: any) => {
    // 删除模型
    if (v?.type === EventEmitterType.deleteModel) {
      setDelModel(v.model)
      setDelProvider(v.provider)
      setShowConfirmDelete(true)
    }
    // 删除模型供应商
    if (v?.type === EventEmitterType.deleteProvider) {
      setDelProvider(v.provider)
      setShowConfirmDelete(true)
    }
    // 设置模型
    if (v?.type === EventEmitterType.setUpModel) {
      showModelProviderModal(
        v.provider,
        ConfigurationMethodEnum.customizableModel,
        (v.model
          ? {
            __model_name: v.model?.model,
            __model_type: v.model?.model_type,
          }
          : undefined),
      )
    }
    // 设置供应商
    if (v?.type === EventEmitterType.setUpProvider) {
      showModelProviderModal(
        v.provider,
        ConfigurationMethodEnum.predefinedModel,
      )
    }
    // 添加模型
    if (v?.type === EventEmitterType.addModel) {
      showModelProviderModal(
        v.provider,
        ConfigurationMethodEnum.customizableModel,
        (v.model
          ? {
            __model_name: v.model?.model,
            __model_type: v.model?.model_type,
          }
          : undefined),
      )
    }
    // 新增供应商
    if (v?.type === EventEmitterType.addProvider)
      setShowProviderModal(true)
  })

  return (
    <>
      {/* 模型类型tab */}
      <Tabs className='mb-6' activeKey={activeTab} items={tabList} onChange={value => setActiveTab(value as ProviderType)}></Tabs>
      {/* provider部分 */}
      <div className='!shrink h-full overflow-auto'>
        {
          activeTab === 'offical' && <OfficalModelProviderPage
            providers={officalProviders}
          ></OfficalModelProviderPage>
        }
        {
          activeTab === 'third-party' && <ThirdPartyModelProviderPage
            localProviders={localProviders}
            cloudProviders={cloudProviders}
            notConfigedProviders={notConfigedProviders}
          ></ThirdPartyModelProviderPage>
        }
      </div>
      {/* 模型操作确认 */}
      <Confirm
        title={t('account.modelProvider.deleteModalTitle')}
        content={t('account.modelProvider.deleteModalTip')}
        isShow={showConfirmDelete}
        onCancel={() => setShowConfirmDelete(false)}
        onConfirm={onConfirmDelete}
      ></Confirm>
      {/* 添加公有云模型modal */}
      <ProviderModal
        providerList={notConfigedProviders}
        isShow={showProviderModal}
        onSave={(provider, field) => {
          updateModelProviderInfo(provider, field)
          setShowProviderModal(false)
        }}
        onCancel={() => setShowProviderModal(false)}
      ></ProviderModal>
    </>
  )
}

export default ModelProviderPage
