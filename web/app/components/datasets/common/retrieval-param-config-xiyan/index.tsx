'use client'
import React, { forwardRef, useEffect, useImperative<PERSON><PERSON>le, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Switch } from 'antd'
import WeightedScore from './weighted-score'
import {
  RETRIEVE_METHOD,
  RerankingModeEnum,
  WeightedScoreEnum,
} from '@/types/datasets'
import type { RetrievalConfig } from '@/types/datasets'
import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'
import { useModelListAndDefaultModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'

import { DEFAULT_WEIGHTED_SCORE } from '@/config/dataset'

// 公共组件
import ScoreThresholdItem from '@/app/components/datasets/common/param-item/score-threshold-item'
import TopKItem from '@/app/components/datasets/common/param-item/top-k-item'
import Tooltip from '@/app/components/base/tooltip'
import cn from '@/utils/classnames'

type Props = {
  type: RETRIEVE_METHOD
  value?: RetrievalConfig
  onChange: (value: RetrievalConfig) => void
  showConfig?: boolean
  showTopKItem?: boolean
  layoutMethod?: 'horizontal' | 'vertical'
}

const RetrievalParamConfigXiyan = forwardRef(
  (
    {
      type,
      value,
      onChange,
      showConfig = false,
      showTopKItem = false,
      layoutMethod = 'horizontal',
    }: Props,
    ref,
  ) => {
    const { t } = useTranslation()
    const {
      defaultModel: rerankDefaultModel,
      modelList: rerankModelList,
      mutateModelList: mutateRerankModelList,
      isModelListLoading: isRerankModelListLoading,
    } = useModelListAndDefaultModel(ModelTypeEnum.rerank)

    // 是否为倒排索引
    const isEconomical = type === RETRIEVE_METHOD.invertedIndex
    // 是否为混合检索
    const isHybridSearch = type === RETRIEVE_METHOD.hybrid
    // 是否为全文检索
    const isFulltext = type === RETRIEVE_METHOD.fullText
    // 是否为向量检索
    const isSemantic = type === RETRIEVE_METHOD.semantic
    // 当前配置值-作为默认值
    const [currentValue, setCurrentValue] = useState<RetrievalConfig>({
      search_method: type,
      reranking_enable: !!isHybridSearch,
      score_threshold_enabled: true,
      reranking_model: {
        reranking_provider_name: rerankDefaultModel?.provider.provider || '',
        reranking_model_name: rerankDefaultModel?.model || '',
      },
      reranking_mode: RerankingModeEnum.WeightedScore,
      top_k: 3,
      score_threshold:
        value?.score_threshold !== undefined
          ? value?.score_threshold_enabled === false
            ? 0
            : 0.5
          : 0.5,
      weights: {
        weight_type: WeightedScoreEnum.Customized,
        vector_setting: {
          vector_weight: DEFAULT_WEIGHTED_SCORE.other.semantic,
          embedding_provider_name: '',
          embedding_model_name: '',
        },
        keyword_setting: {
          keyword_weight: DEFAULT_WEIGHTED_SCORE.other.keyword,
        },
      },
    })

    // rerankmodel值
    const rerankModel = (() => {
      if (currentValue.reranking_model) {
        return {
          provider_name: currentValue.reranking_model.reranking_provider_name,
          model_name: currentValue.reranking_model.reranking_model_name,
        }
      }
      else if (rerankDefaultModel) {
        return {
          provider_name: rerankDefaultModel.provider.provider,
          model_name: rerankDefaultModel.model,
        }
      }
    })()

    // 参数配置初始化
    useEffect(() => {
      if (value?.search_method === type)
        setCurrentValue(value)
    }, [type, value])

    useImperativeHandle(ref, () => ({
      getConfig() {
        return currentValue
      },
      resetConfig() {
        setCurrentValue({
          search_method: type,
          reranking_enable: !!isHybridSearch,
          score_threshold_enabled: true,
          reranking_model: {
            reranking_provider_name:
              rerankDefaultModel?.provider.provider || '',
            reranking_model_name: rerankDefaultModel?.model || '',
          },
          reranking_mode: RerankingModeEnum.WeightedScore,
          top_k: 3,
          score_threshold:
            value?.score_threshold !== undefined
              ? value?.score_threshold_enabled === false
                ? 0
                : 0.5
              : 0.5,
          weights: {
            weight_type: WeightedScoreEnum.Customized,
            vector_setting: {
              vector_weight: DEFAULT_WEIGHTED_SCORE.other.semantic,
              embedding_provider_name: '',
              embedding_model_name: '',
            },
            keyword_setting: {
              keyword_weight: DEFAULT_WEIGHTED_SCORE.other.keyword,
            },
          },
        })
      },
    }))

    return (
      <div>
        {/* 为混合检索模式下-权重配置 */}
        {isHybridSearch && (
          <WeightedScore
            value={{
              value: [
                currentValue.weights!.vector_setting.vector_weight,
                currentValue.weights!.keyword_setting.keyword_weight,
              ],
            }}
            onChange={(v) => {
              onChange({
                ...currentValue,
                weights: {
                  ...currentValue.weights!,
                  vector_setting: {
                    ...currentValue.weights!.vector_setting,
                    vector_weight: v.value[0],
                  },
                  keyword_setting: {
                    ...currentValue.weights!.keyword_setting,
                    keyword_weight: v.value[1],
                  },
                },
              })
            }}
          />
        )}
        {/* 最大召回次数 */}
        {showTopKItem && (
          <TopKItem
            className={cn(
              layoutMethod === 'horizontal'
                ? 'flex items-center h-8 mt-2'
                : 'flex flex-col',
            )}
            value={currentValue.top_k}
            onChange={(_key, v) => {
              onChange({
                ...currentValue,
                top_k: v,
              })
            }}
            labelClassName={cn(
              layoutMethod === 'horizontal'
                ? 'w-[134px] flex !justify-end'
                : 'w-[134px] flex !justify-start ml-4',
            )}
            valueClassName={cn(
              layoutMethod === 'horizontal' ? 'ml-4 !mt-0' : 'ml-4 !mt-0 pr-4',
            )}
            enable={true}
          />
        )}
        {/* 不是倒排索引情况下 */}
        {!isEconomical && showConfig && (
          <>
            {/* 结果重排 */}
            <div className="flex h-9 items-center">
              <div
                className={cn(
                  layoutMethod === 'horizontal'
                    ? 'w-[134px] flex justify-end items-center text-gray-G1 shrink-0 text-S3 leading-H3'
                    : 'w-[80px] flex justify-start ml-4 items-center text-gray-G1 shrink-0 text-S3 leading-H3',
                )}
              >
                <div
                  className="mr-1 truncate"
                  title={t('account.modelProvider.resultRearrangement')!}
                >
                  {t('account.modelProvider.resultRearrangement')}
                </div>
                <Tooltip
                  popupContent={
                    <div className="w-[200px]">
                      {t('account.modelProvider.resultRearrangementTip')}
                    </div>
                  }
                ></Tooltip>
              </div>
              <Switch
                className="ml-4 shrink-0"
                size="small"
                value={currentValue.reranking_enable}
                onChange={(v) => {
                  onChange({
                    ...currentValue,
                    reranking_enable: v,
                  })
                }}
              />
            </div>
            {currentValue.reranking_enable && (
              <>
                {/* 模型选择 */}
                <div
                  className={cn(
                    layoutMethod === 'horizontal'
                      ? 'flex h-9 items-center'
                      : 'flex flex-col my-3',
                  )}
                >
                  <div
                    className={cn(
                      layoutMethod === 'horizontal'
                        ? 'flex justify-end items-center shrink-0 w-[134px]'
                        : 'flex justify-start ml-4 items-center shrink-0 w-full pb-1',
                    )}
                  >
                    <div
                      title={t('account.modelProvider.modelSelect')!}
                      className="mr-1 text-gray-G1 truncate shrink-0 text-S3 leading-H3"
                    >
                      {t('account.modelProvider.modelSelect')}
                    </div>
                    <Tooltip
                      popupContent={
                        <div className="w-[200px]">
                          {t('account.modelProvider.rerankModel.tip')}
                        </div>
                      }
                    ></Tooltip>
                  </div>

                  <ModelSelector
                    loading={isRerankModelListLoading}
                    wrapperClassName={cn(
                      layoutMethod === 'horizontal'
                        ? 'w-full shrink ml-4'
                        : 'w-full shrink ml-4 pr-4',
                    )}
                    triggerClassName={'w-full'}
                    defaultModel={
                      rerankModel && {
                        provider: rerankModel.provider_name,
                        model: rerankModel.model_name,
                      }
                    }
                    modelList={rerankModelList}
                    readonly={!currentValue.reranking_enable}
                    onSelect={(v) => {
                      onChange({
                        ...currentValue,
                        reranking_model: {
                          reranking_provider_name: v.provider,
                          reranking_model_name: v.model,
                        },
                      })
                    }}
                    onFetch={mutateRerankModelList}
                  />
                </div>
                {/* 相似度匹配值 */}
                <ScoreThresholdItem
                  className={cn(
                    layoutMethod === 'horizontal'
                      ? 'flex items-center h-8 mt-2'
                      : 'flex flex-col',
                  )}
                  value={currentValue.score_threshold}
                  labelClassName={cn(
                    layoutMethod === 'horizontal'
                      ? 'w-[134px] flex !justify-end'
                      : 'w-[134px] flex !justify-start ml-4',
                  )}
                  valueClassName={cn(
                    layoutMethod === 'horizontal' ? 'ml-4 !mt-0' : 'ml-4 !mt-0 pr-4',
                  )}
                  onChange={(_key, v) => {
                    onChange({
                      ...currentValue,
                      score_threshold: v,
                    })
                  }}
                  enable={true}
                  hasSwitch={false}
                  onSwitchChange={(_key, v) => {
                    onChange({
                      ...currentValue,
                    })
                  }}
                />
              </>
            )}

          </>
        )}
      </div>
    )
  },
)

RetrievalParamConfigXiyan.displayName = 'RetrievalParamConfigXiyan'

export default React.memo(RetrievalParamConfigXiyan)
