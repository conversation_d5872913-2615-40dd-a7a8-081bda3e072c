'use client'
import type { FC } from 'react'
import React, { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Form } from 'antd'
import type { WorkflowContentRef } from '../detail/workflow-tool'
import { WorkflowToolContent } from '../detail/workflow-tool'
import type { CustomContentRef } from '../detail/custom-tool'
import { CustomCollectionContent } from '../detail/custom-tool'
import type { McpContentRef } from '../detail/mcp-tool'
import { MCPCollectionContent } from '../detail/mcp-tool'
import style from './styles/index.module.css'
import type { WorkflowToolProviderRequest } from '@/types/api/tools'
import type { Collection } from '@/types/tools'
import { useSystemContext } from '@/context/system-context'

/* 公共组件 */
import cn from '@/utils/classnames'
import Modal from '@/app/components/base/modal'
import Button from '@/app/components/base/button'
import {
  AgentTagIcon,
  WorkflowTagIcon,
} from '@/app/components/base/icons/src/vender/line/tag'

// 工具模式
enum ToolMode {
  Workflow = 'workflow', // 工作流
  OpenAPISchema = 'Open-API-schema', // Open-API-schema
  MCP = 'MCP', // MCP
}

type Props = {
  collection?: Collection
  appId?: string
  onHide: () => void
  onCreate?: (
    data?: WorkflowToolProviderRequest &
    Partial<{
      workflow_app_id: string
      workflow_tool_id: string
    }>
  ) => void
}
// Add and Edit
const CreateToolModal: FC<Props> = ({
  collection,
  appId,
  onHide,
  onCreate,
}) => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const isAdd = !collection && !appId

  const workflowChildRef = useRef<WorkflowContentRef>()
  const OpenAPISchemaChildRef = useRef<CustomContentRef>()
  const MCPChildRef = useRef<McpContentRef>()

  // 是否为应用解耦
  const { isAppDecoupled } = useSystemContext()
  // 工具类型
  const [toolMode, setToolMode] = useState<ToolMode>(!isAppDecoupled ? ToolMode.Workflow : ToolMode.OpenAPISchema)
  // 是否禁用
  const [isDisabled, setIsDisabled] = useState(false)

  // 确认新增或编辑
  const onConfirm = async () => {
    if (toolMode === 'workflow') {
      if (workflowChildRef?.current)
        workflowChildRef?.current?.handleSave() // 调用子组件的方法
    }
    if (toolMode === 'Open-API-schema') {
      if (OpenAPISchemaChildRef?.current)
        OpenAPISchemaChildRef?.current?.handleSave() // 调用子组件的方法
    }
    if (toolMode === 'MCP') {
      if (MCPChildRef?.current)
        MCPChildRef?.current?.handleSave() // 调用子组件的方法
    }
    onHide()
  }
  // 弹窗标题
  const modalTitle = isAdd
    ? t('tools.modal.createToolTitle')
    : t('tools.modal.toolConfig')

  return (
    <>
      <Modal
        isShow
        onClose={onHide}
        closable
        title={modalTitle}
        className="!w-[800px]"
        footer={
          <>
            <Button
              className="!w-[92px]"
              variant="secondary-accent"
              onClick={onHide}
            >
              {t('common.operation.cancel')}
            </Button>
            <Button
              disabled={isDisabled}
              className="ml-4 !w-[92px]"
              variant="primary"
              onClick={() => {
                onConfirm()
              }}
            >
              {t('common.operation.create')}
            </Button>
          </>
        }
      >
        <Form form={form} layout="vertical">
          {/* 工具类型 */}
          <Form.Item required label={t('tools.info.createType')}>
            <div className="flex gap-3 flex-wrap">
              {/* 工作流 */}
              {!isAppDecoupled && <div
                className={cn(
                  style['tool-mode-item'],
                  toolMode === ToolMode.Workflow && style['tool-active-mode-item'],
                )}
                onClick={() => {
                  setToolMode(ToolMode.Workflow)
                }}
              >
                <WorkflowTagIcon className={style['mode-icon']} />
                <div className={style['mode-title']}>
                  {t('tools.modal.newCreateWorkflowTool')}
                </div>
              </div>}
              {/* 自动编排 */}
              <div
                className={cn(
                  style['tool-mode-item'],
                  toolMode === ToolMode.OpenAPISchema && style['tool-active-mode-item'],
                )}
                onClick={() => {
                  setToolMode(ToolMode.OpenAPISchema)
                }}
              >
                <AgentTagIcon className={style['mode-icon']} />
                <div className={style['mode-title']}>
                  {t('tools.modal.newCreateOpenApiSchemaTool')}
                </div>
              </div>
              {/* 自动编排 */}
              {!isAppDecoupled && <div
                className={cn(
                  style['tool-mode-item'],
                  toolMode === ToolMode.MCP && style['tool-active-mode-item'],
                )}
                onClick={() => {
                  setToolMode(ToolMode.MCP)
                }}
              >
                <AgentTagIcon className={style['mode-icon']} />
                <div className={style['mode-title']}>
                  {t('tools.modal.newCreateMCPTool')}
                </div>
              </div>}
            </div>
          </Form.Item>
        </Form>
        {
          <>
            {/* 通过工作流创建 */}
            {toolMode === 'workflow' && (
              <WorkflowToolContent
                ref={workflowChildRef as any}
                updateDisabled={setIsDisabled}
                onCreate={onCreate}
              ></WorkflowToolContent>
            )}
            {/* 通过OpenAPI schema创建 */}
            {toolMode === 'Open-API-schema' && (
              <CustomCollectionContent
                ref={OpenAPISchemaChildRef as any}
                updateDisabled={setIsDisabled}
                onAdd={onCreate}
              />
            )}
            {/* 通过MCP创建 */}
            {toolMode === 'MCP' && (
              <MCPCollectionContent
                ref={MCPChildRef as any}
                updateDisabled={setIsDisabled}
                onAdd={onCreate}
              />
            )}
          </>
        }
      </Modal>
    </>
  )
}
export default React.memo(CreateToolModal)
