.desc {
  @apply text-sm font-normal text-gray-G2;
}
.content {
  @apply px-8 pb-6 grow overflow-y-auto;
}

.navbar {
  @apply flex shrink-0 justify-between items-center w-full h-[60px] px-6 border-b border-gray-G6;
  background: white;
}
.header {
  @apply shrink-0 px-8 py-4 border-b-gray-G6 border-b items-center flex justify-between;
  background: linear-gradient(103deg, rgba(255, 255, 255, 0.70) 0%, #FFF 100%) !important;
}
.wrap {
  @apply flex w-full h-full;
}
.middle-wrap {
  @apply flex flex-col w-full h-full;
  background: linear-gradient(103deg, rgba(255, 255, 255, 0.70) 0%, #FFF 100%) !important;
  padding-inline: 20%;
  padding-block: 24px;
}
.title {
  @apply flex items-center w-full text-S4 leading-H4 text-gray-G1 mb-4 shrink-0;
  font-weight: 600;
}
.left-part {
  @apply relative pt-6 flex flex-col w-full h-full shrink;
  background: linear-gradient(103deg, rgba(255, 255, 255, 0.70) 0%, #FFF 100%) !important;
}
.left-title {
  @apply flex items-center w-full text-S4 leading-H4 text-gray-G1 px-8 mb-3 shrink-0;
  font-weight: 600;
}
.left-content {
  @apply flex flex-col w-full h-full px-8 pb-6 grow;
}
.left-title .left-content {
  @apply !py-0;
}
.right-part {
  @apply flex flex-col w-[30%] py-6 min-w-[300px] h-full shrink-0 border-l border-gray-G6 bg-transparent;
}
.right-title {
  @apply flex items-center justify-between px-8 mb-4 text-gray-G1 text-S4 leading-H4 shrink-0;
  font-weight: 600;
}
.right-content {
  @apply flex flex-col grow overflow-y-auto px-8;
}

/* ResizableBox 相关样式 */
.resizeTrigger {
  @apply absolute flex justify-center w-3 cursor-col-resize resize-x h-full border-gray-G6 active:border-gray-G5;
  z-index: 10;
}
.verticalDivider {
  @apply m-0 h-full border-gray-G6 active:border-gray-G5;
  border-color: inherit;
}