import type { FC } from 'react'
import { useRef, useState } from 'react'
import { useMount } from 'ahooks'
import { useTranslation } from 'react-i18next'
import { isEqual } from 'lodash-es'
import { Form, Input } from 'antd'
import cn from '@/utils/classnames'
import Button from '@/app/components/base/button'
import Modal from '@/app/components/base/modal'
import type { DataSet } from '@/models/datasets'
import { useToastContext } from '@/app/components/base/toast'
import { updateDatasetSetting } from '@/service/datasets'
import { useModalContext } from '@/context/modal-context'
import type { RetrievalConfig } from '@/types/datasets'
// import RetrievalMethodConfig from '@/app/components/datasets/common/retrieval-method-config'
import RetrievalMethodConfigXiyan from '@/app/components/datasets/common/retrieval-method-config-xiyan'
import EconomicalRetrievalMethodConfig from '@/app/components/datasets/common/economical-retrieval-method-config'
import { ensureRerankModelSelected, isReRankModelSelected } from '@/app/components/datasets/common/check-rerank-model'
import { AlertTriangle } from '@/app/components/base/icons/src/vender/solid/alertsAndFeedback'
import ModelSelector from '@/app/components/account-setting/model-provider-page/model-selector'
import {
  useModelList,
  useModelListAndDefaultModelAndCurrentProviderAndModel,
} from '@/app/components/account-setting/model-provider-page/hooks'
import type { DefaultModel } from '@/app/components/account-setting/model-provider-page/declarations'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import { fetchMembers } from '@/service/common'
import type { Member } from '@/models/common'
import { Close } from '@/app/components/base/icons/src/vender/line/general'
import { useProviderContext } from '@/context/provider-context'

const TextArea = Input.TextArea

type SettingsModalProps = {
  isShowSettingsModal: boolean
  currentDataset: DataSet
  onCancel: () => void
  onSave: (newDataset: DataSet) => void
}

const rowClass = `
  flex justify-between py-3 flex-wrap gap-y-2
`

const labelClass = `
  flex shrink-0
`

const SettingsModal: FC<SettingsModalProps> = ({
  isShowSettingsModal,
  currentDataset,
  onCancel,
  onSave,
}) => {
  const { data: embeddingsModelList, mutate: mutateEmbeddingModelList, isValidating: isEmbeddingModelListLoading } = useModelList(ModelTypeEnum.textEmbedding)
  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  const { t } = useTranslation()
  const { useXIYANRag } = useProviderContext()
  const { notify } = useToastContext()
  const ref = useRef(null)
  const [form] = Form.useForm()

  const { setShowAccountSettingModal } = useModalContext()
  const [loading, setLoading] = useState(false)
  const [localeCurrentDataset, setLocaleCurrentDataset] = useState({ ...currentDataset })
  const [selectedMemberIDs, setSelectedMemberIDs] = useState<string[]>(currentDataset.partial_member_list || [])
  const [memberList, setMemberList] = useState<Member[]>([])

  const [indexMethod, setIndexMethod] = useState(currentDataset.indexing_technique)
  const [embeddingModel, setEmbeddingModel] = useState<DefaultModel>(
    currentDataset?.embedding_model
      ? {
        provider: localeCurrentDataset.embedding_model_provider,
        model: localeCurrentDataset.embedding_model,
      }
      : {
        provider: '',
        model: '',
      },
  )
  const [retrievalConfig, setRetrievalConfig] = useState(localeCurrentDataset?.retrieval_model_dict as RetrievalConfig)

  const handleValueChange = (type: string, value: string) => {
    setLocaleCurrentDataset({ ...localeCurrentDataset, [type]: value })
  }
  const [isHideChangedTip, setIsHideChangedTip] = useState(true)
  const isRetrievalChanged = !isEqual(retrievalConfig, localeCurrentDataset?.retrieval_model_dict) || indexMethod !== localeCurrentDataset?.indexing_technique

  const handleSave = async () => {
    if (loading)
      return
    form.validateFields().then(async (values) => {
      if (
        !isReRankModelSelected({
          rerankDefaultModel,
          isRerankDefaultModelValid: !!isRerankDefaultModelValid,
          rerankModelList,
          retrievalConfig,
          indexMethod,
        })
      ) {
        notify({ type: 'error', message: t('dataset.placeholder.rerankModel') })
        return
      }
      const postRetrievalConfig = ensureRerankModelSelected({
        rerankDefaultModel: rerankDefaultModel!,
        retrievalConfig,
        indexMethod,
      })
      try {
        setLoading(true)
        const { id, name, description, permission } = localeCurrentDataset
        const requestParams = {
          datasetId: id,
          body: {
            name,
            description,
            permission,
            indexing_technique: indexMethod,
            retrieval_model: {
              ...postRetrievalConfig,
              score_threshold: postRetrievalConfig.score_threshold,
              score_threshold_enabled: true,
            },
            embedding_model: embeddingModel.model,
            embedding_model_provider: embeddingModel.provider,
          },
        } as any
        if (permission === 'partial_members') {
          requestParams.body.partial_member_list = selectedMemberIDs.map((id) => {
            return {
              user_id: id,
              role: memberList.find(member => member.id === id)?.role,
            }
          })
        }
        await updateDatasetSetting(requestParams)
        notify({ type: 'success', message: t('common.actionMsg.modifiedSuccessfully') })
        onSave({
          ...localeCurrentDataset,
          indexing_technique: indexMethod,
          retrieval_model_dict: postRetrievalConfig,
        })
      }
      catch (e) {
        notify({ type: 'error', message: t('common.actionMsg.modifiedUnsuccessfully') })
      }
      finally {
        setLoading(false)
      }
    })
  }

  const getMembers = async () => {
    const { accounts } = await fetchMembers({ url: '/workspaces/current/members', params: {} })
    if (!accounts)
      setMemberList([])
    else
      setMemberList(accounts)
  }

  useMount(() => {
    getMembers()
  })

  return (
    <Modal
      title={t('dataset.action.config')}
      isShow={isShowSettingsModal}
      onClose={onCancel}
      closable
      footer={(
        <div className='flex items-center justify-end space-x-4'>
          <Button
            onClick={onCancel}
            variant={'secondary-accent'}
            className='mr-4'
          >
            {t('common.operation.cancel')}
          </Button>
          <Button
            variant='primary'
            disabled={loading}
            onClick={handleSave}
          >
            {t('common.operation.save')}
          </Button>
        </div>
      )}
    >
      {/* Body */}
      <div className='pb-[24px]'>
        {/* <Form initialValues={{ name: localeCurrentDataset.name || '', description: localeCurrentDataset.description || '' }} form={form} layout='vertical'>
          <Form.Item
            required
            label={
              <div className='inline-flex items-center'>
                <span className='pr-1'>{t('datasetCreation.stepOne.form.name')}</span>
                <Tooltip popupContent={t('datasetCreation.stepOne.form.nameDescription')}></Tooltip>
              </div>
            }
            name={'name'}
            validateTrigger='onBlur'
            validateFirst={true}
            rules={[
              {
                required: true,
                whitespace: true,
                message: t('common.validate.emptyError', {
                  name: t('datasetCreation.stepOne.form.name'),
                }) as string,
              },
              {
                pattern: /^[a-zA-Z0-9\u4E00-\u9FA5()-.]{1,50}$/,
                message: t('dataset.notify.nameError') || '',
              },
            ]}
          >
            <Input
              onChange={e => handleValueChange('name', e.target.value)}
              className='block h-9'
              placeholder={t('dataset.placeholder.namePlaceholder') || ''}
            />
          </Form.Item>
          <Form.Item
            name={'description'}
            label={t('dataset.info.desc')}
            rules={[
              {
                required: true,
                whitespace: true,
              },
            ]}
            validateTrigger='onBlur'
          >
            <TextArea
              onChange={e => handleValueChange('description', e.target.value)}
              className='resize-none'
              placeholder={t('dataset.placeholder.descPlaceholder') || ''}
            />
          </Form.Item>
          <Form.Item label={t('dataset.permission.permissions')}>
            <PermissionSelector
              disabled={!localeCurrentDataset?.embedding_available}
              permission={localeCurrentDataset.permission}
              value={selectedMemberIDs}
              onChange={v => handleValueChange('permission', v!)}
              onMemberSelect={setSelectedMemberIDs}
              memberList={memberList}
            />
          </Form.Item>
        </Form> */}
        {/* <div>
          <div className={labelClass}>
            <div>{t('dataset.permission.permissions')}</div>
          </div>
          <div className='w-full'>

          </div>
        </div> */}
        {/* <div className="w-full h-0 border-b-[0.5px] border-b-gray-200 my-2"></div> */}
        {/* <div className={cn(rowClass)}>
          <div className={labelClass}>
            {t('dataset.info.indexMethod')}
          </div>
          <div className='grow'>
            <IndexMethodRadio
              disable={!localeCurrentDataset?.embedding_available}
              value={indexMethod}
              onChange={v => setIndexMethod(v!)}
              itemClassName='sm:!w-[280px]'
            />
          </div>
        </div> */}

        {/* 索引方式 */}
        <div className={rowClass}>
          <div className={cn(labelClass)}>{t('dataset.action.setting')}</div>
          <div className='w-full'>
            {indexMethod === 'high_quality'
              ? (
                // <RetrievalMethodConfig
                <RetrievalMethodConfigXiyan
                  value={retrievalConfig}
                  onChange={setRetrievalConfig}
                />
              )
              : (
                <EconomicalRetrievalMethodConfig
                  value={retrievalConfig}
                  onChange={setRetrievalConfig}
                />
              )}
          </div>
        </div>
        {/* 模型 */}
        {indexMethod === 'high_quality' && (
          <div className={cn(rowClass)}>
            <div className={labelClass}>
              {t('dataset.info.embeddingModel')}
            </div>
            <div className='w-full'>
              <div className='w-full h-9 rounded'>
                <ModelSelector
                  readonly={useXIYANRag}
                  loading={isEmbeddingModelListLoading}
                  triggerClassName=''
                  defaultModel={embeddingModel}
                  modelList={embeddingsModelList}
                  onSelect={(model: DefaultModel) => {
                    setEmbeddingModel(model)
                  }}
                  onFetch={mutateEmbeddingModelList}
                />
              </div>
              {/* <div className='mt-2 w-full text-xs leading-6 text-gray-500'>
                {t('dataset.info.embeddingModelTip')}
                <span className='text-[#155eef] cursor-pointer' onClick={() => setShowAccountSettingModal({ payload: 'provider' })}>{t('common.operation.settings')}</span>
              </div> */}
            </div>
          </div>
        )}
      </div>
      {isRetrievalChanged && !isHideChangedTip && (
        <div className='absolute z-10 left-[30px] right-[30px] bottom-[76px] flex h-10 items-center px-3 rounded border border-[#FEF0C7] bg-[#FFFAEB] shadow-lg justify-between'>
          <div className='flex items-center'>
            <AlertTriangle className='mr-1 w-3 h-3 text-[#F79009]' />
            <div className='leading-[18px] text-xs font-semibold text-gray-700'>{t('dataset.notify.retrieveChangeTip')}</div>
          </div>
          <div className='p-1 cursor-pointer' onClick={(e) => {
            setIsHideChangedTip(true)
            e.stopPropagation()
            e.nativeEvent.stopImmediatePropagation()
          }}>
            <Close className='w-4 h-4 text-gray-500 ' />
          </div>
        </div>
      )}
    </Modal>
  )
}

export default SettingsModal
