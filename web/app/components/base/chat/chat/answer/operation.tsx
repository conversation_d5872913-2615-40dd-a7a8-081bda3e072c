import type { FC, SetStateAction } from 'react'
import {
  memo,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { Button, Divider, Input, Popover, Radio, Space } from 'antd'
import type { RadioChangeEvent } from 'antd/es/radio'
import type { ChatItem } from '../../types'
import { useChatContext } from '../context'
import style from './styles/operation.module.css'
import EditReplyModal from '@/app/components/app/annotation/edit-annotation-modal'
// 公共组件
import cn from '@/utils/classnames'
import CopyBtn from '@/app/components/base/button/copy-button'
import Log from '@/app/components/base/chat/chat/log'
import Tooltip from '@/app/components/base/tooltip'
import { Refresh,Speaker } from '@/app/components/base/icons/src/vender/line/general'
import { ThumbsDown, ThumbsUp } from '@/app/components/base/icons/src/vender/line/alertsAndFeedback'
import { ThumbsDown as ThumbsDownSolid, ThumbsUp as ThumbsUpSolid } from '@/app/components/base/icons/src/vender/solid/alertsAndFeedback'
import Toast from '@/app/components/base/toast'

const { TextArea } = Input

type OperationProps = {
  item: ChatItem
  question: string
  index: number
  // 显示日志操作按钮
  showPromptLog?: boolean
  hasWorkflowProcess: boolean
  // 没有新的聊天输入
  noChatInput?: boolean
  LongWeb?: boolean
  islongModel?: boolean
}
const Operation: FC<OperationProps> = ({
  item,
  question,
  index,
  showPromptLog,
  hasWorkflowProcess,
  LongWeb,
  islongModel=false,
  noChatInput,
}) => {
  const { t } = useTranslation()
  const {
    config,
    onAnnotationAdded,
    onAnnotationEdited,
    onAnnotationRemoved,
    onFeedback,
    onRegenerate,
  } = useChatContext()
  const {
    id,
    isOpeningStatement,
    content: messageContent,
    annotation,
    feedback,
    adminFeedback,
    agent_thoughts,
  } = item
  // 支持管理员赞踩
  const canAdminFeedback = !!config?.supportAnnotation
  // 是否展示回复弹窗
  const [isShowReplyModal, setIsShowReplyModal] = useState(false)
  // 管理员赞踩
  const [localAdminFeedback, setLocalAdminFeedback] = useState(adminFeedback)
  // 用户赞踩
  const [localUserFeedback, setLocalUserFeedback] = useState(feedback)
  
  // 用户踩
  const [isOpenPopover, setIsOpenPopover] = useState(false)
  // const [dislikeRadio, setDislikeRadio] = useState<{ value: string; label: string }[]>([])
  const [isDisabledButton, setIsDisabledButton] = useState(false) // 禁用按钮
  const [radioValue, setRadioValue] = useState<string>('')
  const [textAreaValue, setTextAreaValue] = useState<string>('')
  const [thisLocalUserFeedback, setThisLocalUserFeedback] = useState(feedback) // 页面踩
  const [thisLocalAdminFeedback, setThisLocalAdminFeedback] = useState(feedback) // 预览踩
  const [localUserFeedbackColor, setLocalUserFeedbackColor] = useState(true) // 左侧保持原有样式

  // 回答的文本
  const content = useMemo(() => {
    if (agent_thoughts?.length)
      return agent_thoughts.reduce((acc, cur) => acc + cur.thought, '')

    return messageContent
  }, [agent_thoughts, messageContent])

  const hasAnnotation = !!annotation?.id
  type FeedbackRating = {
    rating: 'like' | 'dislike' | null
    label?: string
    content?: string
  }

  useEffect(() => {
    // console.log(radioValue, textAreaValue, '===更新===')
  }, [radioValue, textAreaValue, localUserFeedback, localAdminFeedback, localUserFeedbackColor])

  useEffect(() => {

  }, [item])

  const onClose = (type?: string) => {
    // 关闭后清空
    setRadioValue('')
    setTextAreaValue('')
    setIsOpenPopover(false)
    // setIsDisabledButton(true)
    if (type === 'close') {
      setLocalUserFeedback({ rating: thisLocalUserFeedback?.rating ?? null })
      setLocalAdminFeedback({ rating: thisLocalAdminFeedback?.rating ?? null })
    }
  }
  // 处理管理员赞踩情况
  // const handleFeedback = async (rating: 'like' | 'dislike' | null) => {
  //   const currentRating = canAdminFeedback ? localAdminFeedback?.rating : localUserFeedback?.rating
  //   const isCancelRating = currentRating === rating
  //   const value = isCancelRating ? null : rating

  //   await onFeedback?.(id, { rating: value })
  //   if (!config?.supportFeedback || !onFeedback)
  //     return
  //   if (canAdminFeedback)
  //     setLocalAdminFeedback({ rating: value })
  //   else
  //     setLocalUserFeedback({ rating: value })
  // }
  const handleFeedback = async (rating: 'like' | 'dislike' | null, ratingType?: string) => {
    const currentRating = canAdminFeedback ? localAdminFeedback?.rating : localUserFeedback?.rating
    const isCancelRating = currentRating === rating
    let value = isCancelRating ? null : rating

    if (value === 'like' && (localUserFeedback?.rating === 'dislike' || localAdminFeedback?.rating === 'dislike')) {
      setLocalUserFeedback({ rating: value })
      setLocalAdminFeedback({ rating: value })
    }
    if (value && ratingType === 'dislikeReason') { // 踩原因层
      setThisLocalUserFeedback(localUserFeedback)
      setLocalUserFeedback({ rating: value })
      setThisLocalAdminFeedback(localAdminFeedback)
      setLocalAdminFeedback({ rating: value })
      setLocalUserFeedbackColor(false)
      // setLocalUserFeedbackColor(true)
      return
    }
    if (value === null && ratingType === 'dislikeReason') { // 取消踩
      if (isOpenPopover) { // 拦截
        onClose()
        return
      }
      setLocalUserFeedback({ rating: value })
      setLocalAdminFeedback({ rating: value })
      onClose()
    }
    if (value === null && ratingType === 'dislike') { // 提交踩
      value = 'dislike'
      onClose()
    }
    const body: FeedbackRating = { rating: value }
    if (value !== null && ratingType === 'dislike') {
      // 踩=>反馈
      // body.rating = value || 'dislike'
      body.label = radioValue
      body.content = textAreaValue
      // console.log(radioValue, textAreaValue, '===反馈内容===')
    }

    // await onFeedback?.(id, { rating: value })
    await onFeedback?.(id, body)
    if (!config?.supportFeedback || !onFeedback)
      return
    if (canAdminFeedback)
      setLocalAdminFeedback({ rating: value })
    else
      setLocalUserFeedback({ rating: value })
  }
  useEffect(() => {
    // setLocalUserFeedback(feedback)
  }, [feedback, radioValue])

  // 弹窗状态变更
  const handleOpenChange = (newOpenPopover: boolean) => {
    // console.log('handleOpenChange', newOpenPopover)
    setIsOpenPopover(newOpenPopover)
    if (newOpenPopover) {
      if (radioValue)
        setIsDisabledButton(false)
      else
        setIsDisabledButton(true)
    }
    else {
      onClose('close')
    }
  }
  const dislikeRadio = [
    { value: '答非所问', label: '答非所问' },
    { value: '有害/不安全', label: '有害/不安全' },
    { value: '虚假信息', label: '虚假信息' },
    { value: '逻辑问题', label: '逻辑问题' },
    { value: '格式问题', label: '格式问题' },
    { value: '没有帮助', label: '没有帮助' },
  ]
  const generateRadioButtons = (items: { value: string; label: string }[]) => {
    return items.map(item => (
      <Radio key={item.value} value={item.value} className=' text-center'>
        {item.label}
      </Radio>
    ))
  }
  const onRadioChange = (e: RadioChangeEvent) => {
    setIsDisabledButton(false)
    setRadioValue(e?.target?.value)
  }
  const onTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (e?.target?.value)
      setIsDisabledButton(false)
    setTextAreaValue(e?.target?.value)
  }
  // 提交反馈
  const onConfirm = async () => {
    if (!radioValue && !textAreaValue) {
      Toast.notify({
        type: 'warning',
        message: t('appDebug.operation.disagreeContent.reason'),
      })
      return
    }
    // 调用提交反馈接口
    handleFeedback('dislike', 'dislike')
    // onClose('confirm')
    onClose()
  }
  const DislikeReasonSurvey = () => {
    if (!Array.isArray(dislikeRadio) || dislikeRadio.length === 0)
      return <div>加载中...</div>

    return (
      <div className='p-[20px] max-w-[390px]'>
        <div
          className='mb-[20px] text-[#181818] leading-[20px] text-[18px] font-[600]'
        >
          { t('appDebug.operation.disagreeContent.feedback') }
        </div>
        <div className='flex flex-col items-center justify-center'>
          <div>
            <Radio.Group optionType="button" onChange={ onRadioChange } value={ radioValue }>
              <Space wrap={ true }>
                {generateRadioButtons(dislikeRadio)}
              </Space>
            </Radio.Group>
          </div>
          <div className='mt-[10px]' style={{ width: '100%' }}>
            <TextArea
              rows={4}
              placeholder={t('appDebug.operation.disagreeContent.textAreaPlaceholder') || ''}
              onChange={onTextAreaChange}
              value={textAreaValue}
            />
          </div>
          <div className='mt-[20px] flex items-end justify-end ' style={{ width: '100%' }}>
            <Space>
              <Button disabled={isDisabledButton} onClick={onClose}>{t('appDebug.operation.disagreeContent.cancel')}</Button>
              <Button disabled={isDisabledButton} type="primary" onClick={onConfirm}>{t('appDebug.operation.disagreeContent.save')}</Button>
            </Space>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className='flex items-center justify-between mt-3'>
        {/* 用户赞踩 */}
        {
          (canAdminFeedback && localUserFeedback?.rating && localUserFeedbackColor)
            ? (
              <Tooltip
                popupContent={localUserFeedback?.rating === 'like' ? t('appDebug.operation.agree') : t('appDebug.operation.disagree')}
              >
                <div className={style.operationItem}>
                  {localUserFeedback?.rating === 'like' ? <ThumbsUp className='w-[14px] h-[14px] text-primary-P1'></ThumbsUp> : <ThumbsDown className='w-4 h-4 text-primary-P1'></ThumbsDown>}
                </div>
              </Tooltip>
            )
            : <div></div>
        }
        <div className={style.operationListWrap}>
          {/* 复制 */}
          {!isOpeningStatement && !LongWeb && (
            <>
              <CopyBtn wrapClassName={style.operationItem} value={content} />
              <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
            </>
          )}
          {/* 日志 */}
          {!isOpeningStatement && showPromptLog && (
            <>
              <Log logItem={item} className={style.operationItem} />
              <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
            </>
          )}
          {/* 刷新 */}
          {/* {!isOpeningStatement && !noChatInput
            && <>
              <Tooltip
                popupContent={t('appApi.regenerate') as string}
              >
                <div
                  className={style.operationItem}
                  onClick={() => onRegenerate?.(item)}
                >
                  <Refresh className={cn('w-4 h-4')} />
                </div>
              </Tooltip>
              <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
            </>
          } */}
          {/* {(!isOpeningStatement && config?.supportAnnotation && config.annotation_reply?.enabled) && (
          <AnnotationCtrlBtn
            appId={config?.appId || ''}
            messageId={id}
            annotationId={annotation?.id || ''}
            className='hidden group-hover:flex ml-1 shrink-0'
            cached={hasAnnotation}
            query={question}
            answer={content}
            onAdded={(id, authorName) => onAnnotationAdded?.(id, authorName, question, content, index)}
            onEdit={() => setIsShowReplyModal(true)}
            onRemoved={() => onAnnotationRemoved?.(index)}
          />
        )}
        {
          annotation?.id && (
            <div
              className='relative box-border flex items-center justify-center h-7 w-7 p-0.5 rounded-lg bg-white cursor-pointer text-[#444CE7] shadow-md group-hover:hidden'
            >
              <div className='p-1 rounded-lg bg-[#EEF4FF] '>
                <MessageFast className='w-4 h-4' />
              </div>
            </div>
          )
        } */}
          {/* 支持回调，且没有赞踩的情况下 */}
          {
            config?.supportFeedback && onFeedback && !isOpeningStatement && (
              <>
                {/* <Divider className='mx-2 h-[14px]' type='vertical'></Divider> */}
                {/* 点赞  */}
                <Tooltip popupContent={t('appDebug.operation.agree')}>
                  <div className={style.operationItem} onClick={() => handleFeedback('like')}>
                    {canAdminFeedback
                      ? (
                        localAdminFeedback?.rating === 'like'
                          ? <ThumbsUpSolid className={'text-primary-P1 w-4 h-4'} />
                          : <ThumbsUp className={'w-[14px] h-[14px]'} />
                      )
                      : <ThumbsUp
                        className={cn(
                          localUserFeedback?.rating === 'like' && 'text-primary-P1',
                          'w-[14px] h-[14px]',
                        )}
                      />
                    }
                  </div>
                </Tooltip>
                <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
                {/* 点踩 */}
                <Tooltip popupContent={t('appDebug.operation.disagree')}>
                  {/* <div className={style.operationItem} onClick={() => handleFeedback('dislike')}>
                    {canAdminFeedback
                      ? (
                        localAdminFeedback?.rating === 'dislike'
                          ? <ThumbsDownSolid className={'text-primary-P1 w-4 h-4'} />
                          : <ThumbsDown className={'w-[14px] h-[14px]'} />
                      )
                      : <ThumbsDown
                        className={cn(
                          localUserFeedback?.rating === 'dislike' && 'text-primary-P1',
                          'w-[14px] h-[14px] cursor-pointer',
                        )}
                      />
                    }
                  </div> */}
                  <Popover
                    className="p-0"
                    arrow={false}
                    content={DislikeReasonSurvey}
                    trigger='click'
                    open={isOpenPopover}
                    onOpenChange={handleOpenChange}
                    // style={{ padding: '15px' }}
                  >
                    <div
                      className={style.operationItem}
                      onClick={() => handleFeedback('dislike', 'dislikeReason')}
                    >
                      {canAdminFeedback
                        ? (
                          localAdminFeedback?.rating === 'dislike'
                            ? <ThumbsDownSolid className={'text-primary-P1 w-4 h-4'} />
                            : <ThumbsDown className={'w-[14px] h-[14px]'} />
                        )
                        : <ThumbsDown
                          className={cn(
                            localUserFeedback?.rating === 'dislike' && 'text-primary-P1',
                            'w-[14px] h-[14px] cursor-pointer',
                          )}
                        />
                      }
                    </div>
                  </Popover>
                </Tooltip>
                <Divider className='mx-2 h-[14px]' type='vertical'></Divider>
                
              </>
            )
          }
          <div className='AiDiv'>内容由AI生成，仅供参考</div>
        </div>
      </div>
      <EditReplyModal
        isShow={isShowReplyModal}
        onHide={() => setIsShowReplyModal(false)}
        query={question}
        answer={content}
        onEdited={(editedQuery, editedAnswer) => onAnnotationEdited?.(editedQuery, editedAnswer, index)}
        onAdded={(annotationId, authorName, editedQuery, editedAnswer) => onAnnotationAdded?.(annotationId, authorName, editedQuery, editedAnswer, index)}
        appId={config?.appId || ''}
        messageId={id}
        annotationId={annotation?.id || ''}
        createdAt={annotation?.created_at}
        onRemove={() => onAnnotationRemoved?.(index)}
      />
    </>
  )
}

export default memo(Operation)
