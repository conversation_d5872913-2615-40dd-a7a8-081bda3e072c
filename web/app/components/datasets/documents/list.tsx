'use client'
import type { FC } from 'react'
import React, { useCallback, useEffect, useState } from 'react'
import { useBoolean } from 'ahooks'
import { useRouter } from 'next/navigation'
import { useTranslation } from 'react-i18next'
import dayjs from 'dayjs'
import type { TableColumnsType } from 'antd'
import { Table } from 'antd'

import { useContext } from 'use-context-selector'

import {
  QuestionCircleOutlined,
} from '@ant-design/icons'
import s from './style.module.css'
import RenameModal from './rename-modal'
import DetailModal from './detail-modal'
import OperationAction from './operation'
import { useErrorDocs } from './retry-button'
import { deleteDocument } from '@/service/datasets'
import { DataSourceType, type SimpleDocumentDetail } from '@/models/datasets'
import StatusItem from '@/app/components/datasets/common/status-indicator'
import { getDocStatus } from '@/app/components/datasets/utils'
// 公共的工具和方法
import useTimestamp from '@/hooks/use-timestamp'
import { Edit } from '@/app/components/base/icons/src/vender/line/general'
import cn from '@/utils/classnames'
import { formatFileSize, formatNumber2 } from '@/utils/format'
import FileIcon from '@/app/components/base/file-icon'
import Confirm from '@/app/components/base/confirm'
import { asyncRunSafe } from '@/utils'
import { ToastContext } from '@/app/components/base/toast'
import TextButton from '@/app/components/base/button/text-button'
import { useAppContext } from '@/context/app-context'
import Tooltip from '@/app/components/base/tooltip'
import { Warn } from '@/app/components/base/icons/src/public/common'
import { useProviderContext } from '@/context/provider-context'

type LocalDoc = SimpleDocumentDetail & { percent?: number }
type IDocumentListProps = {
  embeddingAvailable: boolean
  documents: LocalDoc[]
  datasetId: string
  onUpdate: () => void
  onDelete: () => void
}

// 索引类型： 高质量|经济
enum CheckResultType {
  CHECKING = 'checking',
  FAILED = 'failed',
  PASS = 'pass',
}

const DocumentList: FC<IDocumentListProps> = ({ embeddingAvailable, documents = [], datasetId, onUpdate, onDelete }) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { notify } = useContext(ToastContext)
  const router = useRouter()
  const { useXIYANRag } = useProviderContext()
  const { hasError, updateErrorStatus } = useErrorDocs(datasetId)
  // 文档列表
  const [localDocs, setLocalDocs] = useState<LocalDoc[]>(documents)
  // 当前选中文档
  const [currDocument, setCurrDocument] = useState<LocalDoc | null>(null)
  // 是否显示重命名弹窗
  const [
    isShowRenameModal,
    { setTrue: setShowRenameModalTrue, setFalse: setShowRenameModalFalse },
  ] = useBoolean(false)
  // 是否显示删除知识库弹窗
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  // 是否显示知识库详情弹窗
  const [showDetailModal, setShowDetailModal] = useState(false)
  // 是否显示安全围栏弹窗
  const [showSecurityFenceModal, setShowSecurityFenceModal] = useState(false)
  const { isCheckContentVip } = useAppContext()

  // 工具函数
  const isFile = (record: LocalDoc) => {
    return record.data_source_type === DataSourceType.FILE
  }

  // 显示重命名弹窗
  const showRenameModal = useCallback(
    (doc: LocalDoc) => {
      setCurrDocument(doc)
      setShowRenameModalTrue()
    },
    [setShowRenameModalTrue],
  )
  // 打开删除文档弹窗
  const openDeleteModal = useCallback((doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowDeleteModal(true)
  }, [])
  // 打开文档详情弹窗
  const openDetailModal = useCallback((doc: LocalDoc) => {
    setCurrDocument(doc)
    setShowDetailModal(true)
  }, [])
  // 查看知识库
  const showDatasetDetail = (doc: LocalDoc) => {
    router.push(`/datasets/${datasetId}/documents/${doc.id}`)
  }
  // 删除知识库
  const deleteDataset = async () => {
    const [e] = await asyncRunSafe(
      deleteDocument({
        datasetId,
        documentId: currDocument!.id,
      }) as Promise<any>,
    )
    if (!e) {
      setShowDeleteModal(false)
      notify({
        type: 'success',
        message: t('common.actionMsg.deleteSuccessfully'),
      })
      onDelete()
    }
  }

  useEffect(() => {
    setLocalDocs(documents)
  }, [documents])

  // 监听文档状态变化
  useEffect(() => {
    const hasErrorDoc = localDocs.some(doc => getDocStatus(doc) === 'error')
    updateErrorStatus(hasErrorDoc)
  }, [localDocs, updateErrorStatus])

  const columns: TableColumnsType<LocalDoc> = [
    {
      title: '',
      key: 'test',
      width: 20,
    },
    {
      title: '#',
      key: 'position',
      width: 60,
      dataIndex: 'position',
      hidden: useXIYANRag,
    },
    // 文件名以及重命名弹窗
    {
      title: t('datasetDocuments.list.table.header.fileName'),
      key: 'fileName',
      render: (_: any, record) => {
        const fileType = isFile(record)
          ? record.data_source_detail_dict?.upload_file?.extension
          : ''
        return (
          <div className="group flex items-center">
            <span className={s.tdValue}>
              {isFile(record) && (
                <FileIcon
                  className={cn(s.commonIcon, 'mr-1.5')}
                  type={
                    record?.data_source_info?.upload_file?.extension ?? fileType
                  }
                ></FileIcon>
              )}
              {record.name}
            </span>
            {useXIYANRag && (
              <Edit
                className="w-4 h-4 text-gray-G3 hover:text-gray-G1 cursor-pointer shrink-0 ml-3"
                onClick={(e) => {
                  e.stopPropagation()
                  showRenameModal(record)
                }}
              />
            )}
            {(record?.check_result === CheckResultType.FAILED) && <> &nbsp;&nbsp;<Tooltip
              popupContent={
                <>
                  <div>{isCheckContentVip ? t('datasetDocuments.securityFence.whitelistCopywriting') : t('datasetDocuments.securityFence.nonWhitelistCopywriting')}</div>
                </>
              }

            >
              <Warn className='w-4 h-4' onClick={(e) => {
                e.stopPropagation()
                setShowSecurityFenceModal(true)
              }}></Warn>
            </Tooltip>
            </>
            }
          </div>
        )
      },
    },
    // 字符数
    {
      title: t('datasetDocuments.list.table.header.words'),
      key: 'word_count',
      render: (_: any, record) => {
        const value = record.word_count as string | number
        if (typeof value === 'string' && value.includes(',')) {
          // 检查是否是"数字,数字"格式（如"1,2"）
          const parts = value.split(',')
          if (
            parts.length === 2
            && !isNaN(Number(parts[0]))
            && !isNaN(Number(parts[1]))
          ) {
            return t('common.info.rowCol', { row: parts[0], col: parts[1] })
          }
          else {
            // 处理千位分隔符情况（如"1,234"）
            const number = parseFloat(value.replace(/,/g, ''))
            return !isNaN(number)
              ? `${number.toFixed(1)} ${t('common.info.characters')}`
              : value
          }
        }
        // 其他情况直接返回原值或格式化的数字
        return typeof value === 'number'
          ? `${formatNumber2(value)} ${t('common.unit.characters')}`
          : value === '0' ? '' : `${value} ${t('common.unit.characters')}`
      },
      width: 100,
      ellipsis: true,
    },
    // 命中次数
    {
      title: t('datasetDocuments.list.table.header.hitCount'),
      key: 'hit_count',
      render: (_: any, record) => formatNumber2(record.hit_count),
      width: 100,
    },
    // 更新时间
    {
      title: t('datasetDocuments.list.table.header.uploadTime'),
      sorter: (a, b) =>
        dayjs(a.created_at).isBefore(dayjs(b.created_at)) ? -1 : 1,
      key: 'uploadTime',
      render: (_: any, record) =>
        formatTime(
          record.created_at,
          t('common.dateFormat.dateTime') as string,
        ),
      width: 250,
    },
    // 索引状态
    {
      title: t('datasetDocuments.list.table.header.status'),
      key: 'status',
      render: (_: any, record) => (
        <div className="flex items-center gap-2">
          <StatusItem status={getDocStatus(record)} />
          {(getDocStatus(record) === 'error' || (record?.error && record.error !== '' && record.error !== 'success')) && (
            <Tooltip popupContent={record?.error}>
              <QuestionCircleOutlined className={s.resultIcon} />
            </Tooltip>
          )}
        </div>
      ),
      width: 100,
    },
    // 操作
    ...(embeddingAvailable
      ? [
        {
          title: t('datasetDocuments.list.table.header.action'),
          render: (_: any, record: LocalDoc) => (
            <div
              className="flex items-center gap-6"
              onClick={e => e.stopPropagation()}
            >
              {<OperationAction
                embeddingAvailable={embeddingAvailable}
                datasetId={datasetId}
                detail={record}
                onUpdate={onUpdate}
              />}
              <TextButton
                size="middle"
                type="primary"
                onClick={() => openDetailModal(record)}
              >
                {`${t('common.info.file')}${t('common.operation.detail')}`}
              </TextButton>
              <TextButton
                size="middle"
                type="primary"
                onClick={() => openDeleteModal(record)}
              >
                {t('common.operation.delete')}
              </TextButton>
            </div>
          ),
          key: 'operation',
          align: 'left' as any,
          width: 240,
        },
      ]
      : []),
  ]
  return (
    <>
      <Table
        size="large"
        columns={columns.filter(col => !col.hidden)}
        pagination={{
          position: ['none', 'none'],
          pageSize: 15,
          showSizeChanger: false,
        }}
        scroll={{ y: 'calc(100vh - 285px)' }}
        dataSource={localDocs}
        className="border-gray-G5 rounded border"
        rowClassName="cursor-pointer"
        onRow={(record) => {
          return {
            onClick: e => showDatasetDetail(record),
          }
        }}
      ></Table>
      {isShowRenameModal && currDocument && (
        <RenameModal
          datasetId={datasetId}
          documentId={currDocument.id}
          name={currDocument.name}
          onClose={setShowRenameModalFalse}
          onSaved={() => {
            onUpdate()
            setShowRenameModalFalse()
          }}
        />
      )}
      {showDeleteModal && (
        <Confirm
          isShow={showDeleteModal}
          title={t('datasetDocuments.list.delete.title')}
          content={t('datasetDocuments.list.delete.content')}
          onConfirm={() => deleteDataset()}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
      {showDetailModal && currDocument
       && <DetailModal
         detail={
           {
             originFileName: currDocument!.data_source_detail_dict?.upload_file?.name || '-',
             originFileSize: formatFileSize(currDocument!.data_source_detail_dict?.upload_file?.size || 0).toString(),
             uploadTime: formatTime(currDocument!.created_at, t('common.dateFormat.dateTime') as string),
             lastUpdateTime: formatTime(currDocument!.updated_at, t('common.dateFormat.dateTime') as string),
           }
         }
         onClose={() => setShowDetailModal(false)}
       ></DetailModal>
      }
      {showSecurityFenceModal
        && <Confirm
          isShow={showSecurityFenceModal}
          title={t('datasetDocuments.securityFence.title')}
          content={isCheckContentVip ? `${t('datasetDocuments.securityFence.whitelistCopywriting')}${t('datasetDocuments.securityFence.content')}` : `${t('datasetDocuments.securityFence.nonWhitelistCopywriting')}`}
          onConfirm={() => { setShowSecurityFenceModal(false) }}
          onCancel={() => {
            setShowSecurityFenceModal(false)
            if (isCheckContentVip) {
              setTimeout(() => {
                setShowSecurityFenceModal(true)
              }, 500)
            }
          }}
        />
      }
    </>
  )
}

export default DocumentList
