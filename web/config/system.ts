import type { SystemFeatures } from '@/types/system'

// 默认系统配置
export const DEFAULT_SYSTEM_FEATURE: SystemFeatures = {
  sso_enforced_for_signin: false,
  sso_enforced_for_signin_protocol: '',
  sso_enforced_for_web: false,
  sso_enforced_for_web_protocol: '',
  enable_web_sso_switch_component: false,
  enable_email_code_login: false,
  enable_email_password_login: false,
  enable_social_oauth_login: false,
  is_allow_create_workspace: false,
  is_allow_register: false,
  is_micro: false,
  enable_sso_login: false,
  enable_private_register: true,
  i18n_enabled: false,
  use_xiyan_server: false,
  is_app_decoupled: false,
}
