'use client'

import { memo, useCallback, useEffect, useMemo, useState } from 'react'
import useS<PERSON> from 'swr'
import { useTranslation } from 'react-i18next'
import { createContext, useContext, useContextSelector } from 'use-context-selector'
import { useSearchParams } from 'next/navigation'
import type { FC, ReactNode } from 'react'
import { useSystemContext } from './system-context'
import Loading from '@/app/components/base/loading'
import ProtocolConfirmModal from '@/app/components/account-setting/protocol-confirm-modal'
import { fetchCurrentWorkspace, fetchUserProfile } from '@/service/common'
import type {
  ICurrentWorkspace,
  UserProfileResponse,
} from '@/models/common'
import { getOperToken } from '@/utils/user'
import Toast from '@/app/components/base/toast'

export type AppContextValue = {
  userProfile: UserProfileResponse
  mutateUserProfile: VoidFunction
  currentWorkspace: ICurrentWorkspace
  canAddOfficialModel: boolean
  canPublishApp: boolean
  isMicroApp: boolean
  isSuperUser: boolean
  canEditVideoLib: boolean
  canViewVideoLib: boolean
  longDocState: boolean
  setLongDocState: (v: boolean) => void
  mutateCurrentWorkspace: VoidFunction
  useSelector: typeof useSelector
  isVip: boolean
  canDataset: boolean
  canViewApp: boolean
  canEditApp: boolean
  canAdmin: boolean
  isParent: boolean
  canAccountSetting: boolean
  isCheckContentVip: boolean
  canCreateApp: boolean
}

const initialWorkspaceInfo: ICurrentWorkspace = {
  id: '',
  name: '',
  plan: '',
  status: '',
  created_at: 0,
  role: 'normal',
  providers: [],
  in_trail: true,
}

const AppContext = createContext<AppContextValue>({
  userProfile: {
    id: '',
    name: '',
    email: '',
    avatar: '',
    client_from: '',
    is_parent: false,
    is_vip: false,
    is_password_set: false,
    super_user: false,
    is_check_content_vip: false,
    video_library_user: false,
    is_confirm: false,
  },
  currentWorkspace: initialWorkspaceInfo,
  isMicroApp: false,
  isSuperUser: false,
  canEditVideoLib: false,
  canViewVideoLib: false,
  canAddOfficialModel: false,
  canPublishApp: false,
  longDocState: false,
  setLongDocState: () => {},
  isVip: false,
  isParent: false,
  mutateUserProfile: () => { },
  mutateCurrentWorkspace: () => { },
  useSelector,
  canDataset: false,
  canViewApp: false,
  canEditApp: false,
  canAdmin: false,
  canAccountSetting: false,
  isCheckContentVip: false,
  canCreateApp: false,
})

export function useSelector<T>(selector: (value: AppContextValue) => T): T {
  return useContextSelector(AppContext, selector)
}

export type AppContextProviderProps = {
  children: ReactNode
}

export const AppContextProvider: FC<AppContextProviderProps> = memo(({ children }) => {
  const searchParams = useSearchParams()
  const { t } = useTranslation()
  const { systemFeatures, isPrivate, isAppDecoupled } = useSystemContext()
  // 监听是否为长文
  const [longDocState, setLongDocState] = useState(false)
  // 显示确认协议弹窗
  const [showConfirmProtocol, setShowConfirmProtocolModal] = useState<boolean>(false)
  // 用户信息缓存
  const [userProfile, setUserProfile] = useState<UserProfileResponse>()
  // 用户信息获取
  const { data: userProfileResponse, mutate: mutateUserProfile } = useSWR(
    { url: '/account/profile', params: {} },
    fetchUserProfile,
  )
  // 工作空间获取
  const { data: currentWorkspaceResponse, mutate: mutateCurrentWorkspace } = useSWR(
    userProfile?.is_confirm ? { url: '/workspaces/current', params: {} } : null,
    fetchCurrentWorkspace,
  )
  // 是否可以可以发布应用
  const [canPublishApp] = useState(Boolean(searchParams.get('canPublishApp')))
  // 是否可以新增官方模型
  const [canAddOfficialModel] = useState(Boolean(searchParams.get('canAddOfficialModel')))

  // 当前工作空间
  const [currentWorkspace, setCurrentWorkspace] = useState<ICurrentWorkspace>(initialWorkspaceInfo)
  // 是否为当前工作空间管理者
  const isCurrentWorkspaceManager = useMemo(
    () => ['owner', 'admin'].includes(currentWorkspace.role),
    [currentWorkspace.role],
  )
  // 是否为当前工作空间拥有者
  const isCurrentWorkspaceOwner = useMemo(
    () => currentWorkspace.role === 'owner',
    [currentWorkspace.role],
  )
  // 当前工作区是否可编辑
  const isCurrentWorkspaceEditor = useMemo(
    () => ['owner', 'admin', 'editor'].includes(currentWorkspace.role),
    [currentWorkspace.role],
  )
  // 是否为当前工作空间知识库操作人员
  const isCurrentWorkspaceDatasetOperator = useMemo(
    () => currentWorkspace.role === 'dataset_operator',
    [currentWorkspace.role],
  )
  // 是否为普通成员
  const isCurrentWorkspaceNormal = useMemo(() => currentWorkspace.role === 'normal', [currentWorkspace.role])
  // 是否为超级管理员
  const isSuperUser = useMemo(() => userProfile?.super_user || false, [userProfile])
  // 是否伟白名单用户
  const isVideoLibUser = useMemo(() => userProfile?.video_library_user || false, [userProfile])
  // 是否是主账号
  const isParent = useMemo(() => userProfile?.is_parent || false, [userProfile])
  // 是否是vip用户
  const isVip = useMemo(() => (userProfile?.is_vip && !isPrivate) || false, [isPrivate, userProfile?.is_vip])
  // 是否微微前端嵌套
  const isMicroApp = useMemo(() => (systemFeatures.is_micro && currentWorkspace.plan === 'basic' && (isPrivate || (!isPrivate && (isVip || isParent)))), [currentWorkspace.plan, isParent, isPrivate, isVip, systemFeatures.is_micro])
  // 是否拥有知识库权限
  const canDataset = useMemo(() => {
    return (isPrivate && !isCurrentWorkspaceNormal && (!isAppDecoupled || (isAppDecoupled && isSuperUser))) || (!isPrivate && (isVip || isParent))
  }, [isAppDecoupled, isCurrentWorkspaceNormal, isParent, isPrivate, isSuperUser, isVip])
  // 是否拥有查看应用权限
  const canViewApp = useMemo(() => {
    return (isPrivate && !isCurrentWorkspaceDatasetOperator && (!isAppDecoupled || (isAppDecoupled && isSuperUser))) || !isPrivate
  }, [isAppDecoupled, isCurrentWorkspaceDatasetOperator, isPrivate, isSuperUser])
  // 是否拥有编辑应用权限
  const canEditApp = useMemo(() => {
    return (isPrivate && isCurrentWorkspaceEditor && (!isAppDecoupled || (isAppDecoupled && isSuperUser))) || (!isPrivate && (isVip || isParent))
  }, [isAppDecoupled, isCurrentWorkspaceEditor, isParent, isPrivate, isSuperUser, isVip])
  // 是否可以创建应用权限
  const canCreateApp = useMemo(() => {
    return canEditApp && !isAppDecoupled
  }, [canEditApp, isAppDecoupled])
  // 是否拥有管理员能力
  const canAdmin = useMemo(() => {
    return (isPrivate && isCurrentWorkspaceManager) || (!isPrivate && (isVip || isParent))
  }, [isCurrentWorkspaceManager, isParent, isPrivate, isVip])
  // 是否能账户设置
  const canAccountSetting = useMemo(() => {
    return isPrivate || (!isPrivate && (isVip || isParent))
  }, [isParent, isPrivate, isVip])
  // 是否可以查看视频库
  const canViewVideoLib = useMemo(
    () => (isPrivate && isVideoLibUser && (!isAppDecoupled || (isAppDecoupled && isSuperUser))) || !isPrivate,
    [isAppDecoupled, isPrivate, isSuperUser, isVideoLibUser],
  )
  // 是否可以编辑视频库
  const canEditVideoLib = useMemo(() => {
    return (canViewVideoLib) && isVideoLibUser
  }, [canViewVideoLib, isVideoLibUser])

  // 是否白名单用户
  const isCheckContentVip = useMemo(() => userProfile?.is_check_content_vip || false, [userProfile])

  // 更新用户信息以及版本
  const updateUserProfileAndVersion = useCallback(async () => {
    if (userProfileResponse && !userProfileResponse.bodyUsed) {
      const result = await userProfileResponse.json()
      setUserProfile({
        is_confirm: true,
        ...result,
      })
    }
  }, [userProfileResponse])

  useEffect(() => {
    updateUserProfileAndVersion()
  }, [updateUserProfileAndVersion, userProfileResponse])
  useEffect(() => {
    if (currentWorkspaceResponse)
      setCurrentWorkspace(currentWorkspaceResponse)
  }, [currentWorkspaceResponse])
  useEffect(() => {
    setLongDocState(longDocState)
  }, [longDocState])
  // 动态监听
  useEffect(() => {
    if (isPrivate)
      return
    // 监听窗口可见性变化
    const handleVisibilityChange = () => {
      const userToken = localStorage.getItem('operToken')
      const currentToken = getOperToken()
      if (!userToken) {
        localStorage.setItem('operToken', currentToken || '')
        return
      }
      if (!document.hidden) {
        if (currentToken !== userToken) {
          // 账号信息变更
          Toast.notify({
            type: 'warning',
            message: t('common.actionMsg.accountChange'),
          })
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
      }
    }
    // 监听点击事件
    const handleClick = () => {
      const userToken = localStorage.getItem('operToken')
      const currentToken = getOperToken()
      if (!userToken) {
        localStorage.setItem('operToken', currentToken || '')
        return
      }
      if (currentToken !== userToken) {
        // 账号信息变更
        Toast.notify({
          type: 'warning',
          message: t('common.actionMsg.accountChange'),
        })
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    document.addEventListener('click', handleClick, true)
    handleClick()
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      document.removeEventListener('click', handleClick, true)
    }
  }, [isPrivate, t])
  useEffect(() => {
    if (userProfile) {
      if (!userProfile.is_confirm)
        setShowConfirmProtocolModal(true)
      else
        setShowConfirmProtocolModal(false)
    }
  }, [currentWorkspace.id, setShowConfirmProtocolModal, userProfile])

  if (!userProfile || !currentWorkspace.id) {
    return (
      <>
        <Loading type='app' />
        {
          showConfirmProtocol && (
            <ProtocolConfirmModal onConfirm={mutateUserProfile}></ProtocolConfirmModal>
          )
        }
      </>
    )
  }

  return (
    <AppContext.Provider value={{
      userProfile,
      mutateUserProfile,
      useSelector,
      canPublishApp,
      canAddOfficialModel,
      canEditVideoLib,
      canViewVideoLib,
      currentWorkspace,
      isMicroApp,
      isSuperUser,
      isVip,
      mutateCurrentWorkspace,
      longDocState,
      setLongDocState,
      canDataset,
      canViewApp,
      canEditApp,
      canAdmin,
      isParent,
      canAccountSetting,
      isCheckContentVip,
      canCreateApp,
    }}>
      {children}
    </AppContext.Provider>
  )
})
AppContextProvider.displayName = 'AppContextProvider'

export const useAppContext = () => useContext(AppContext)

export default AppContext
