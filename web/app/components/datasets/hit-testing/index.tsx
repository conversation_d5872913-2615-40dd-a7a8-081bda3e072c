'use client'
import type { FC } from 'react'
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import useSWR from 'swr'
import { cloneDeep, debounce, omit } from 'lodash-es'
import { useContext } from 'use-context-selector'
import { Divider, Input, Pagination, Table, type TableColumnsType } from 'antd'
import SegmentCard from '../documents/detail/segment-card'
import HitSegmentCard from './hit-segment-card'
import { fetchTestingRecords, hitTesting } from '@/service/datasets'
import DatasetDetailContext from '@/context/dataset-detail'
import type {
  HitTestingRecord,
  HitTestingResponse,
  HitTesting as HitTestingType,
} from '@/models/datasets'
import { RETRIEVE_METHOD, type RetrievalConfig } from '@/types/datasets'

import { useModelListAndDefaultModelAndCurrentProviderAndModel } from '@/app/components/account-setting/model-provider-page/hooks'
import { ModelTypeEnum } from '@/app/components/account-setting/model-provider-page/declarations'
import RetrievalMethodConfig from '@/app/components/datasets/common/retrieval-method-config'
import RetrievalMethodConfigXiyan from '@/app/components/datasets/common/retrieval-method-config-xiyan'
import {
  ensureRerankModelSelected,
  isReRankModelSelected,
} from '@/app/components/datasets/common/check-rerank-model'
import SegmentDetail from '@/app/components/datasets/common/segment-detail'
import style from '@/app/components/datasets/styles/style.module.css'
import cn from '@/utils/classnames'
import useTimestamp from '@/hooks/use-timestamp'
import Empty from '@/app/components/base/empty'
import Scrollbar from '@/app/components/base/scrollbar'
import Button from '@/app/components/base/button'
import Toast from '@/app/components/base/toast'
import { asyncRunSafe } from '@/utils'
import ResizableBox from '@/app/components/base/resizable-box'
import { useProviderContext } from '@/context/provider-context'
const limit = 6

// 定义各面板的最小宽度常量
const MIN_LEFT_PANEL_WIDTH = 400
const MIN_QUERY_PANEL_WIDTH = 300
const MIN_RESULT_PANEL_WIDTH = 300

// 定义各面板的初始宽度常量
const INITIAL_QUERY_PANEL_WIDTH = 300
const INITIAL_RESULT_PANEL_WIDTH = 500

type Props = {
  datasetId: string
}

const HitTesting: FC<Props> = ({ datasetId }: Props) => {
  const { t } = useTranslation()
  const { formatTime } = useTimestamp()
  const { dataset: currentDataset } = useContext(DatasetDetailContext)
  const {
    modelList: rerankModelList,
    defaultModel: rerankDefaultModel,
    currentModel: isRerankDefaultModelValid,
  } = useModelListAndDefaultModelAndCurrentProviderAndModel(
    ModelTypeEnum.rerank,
  )
  const { useXIYANRag } = useProviderContext()

  // 提交加载
  const [submitLoading, setSubmitLoading] = useState(false)
  // 召回测试节瓜哦
  const [hitResult, setHitResult] = useState<HitTestingResponse | undefined>() // 初始化记录为空数组
  // 当前分段信息
  const [currParagraph, setCurrParagraph] = useState<{
    paraInfo?: HitTestingType
    showModal: boolean
  }>({ showModal: false })
  // 召回测试文本
  const [text, setText] = useState('')
  // 当前页
  const [currPage, setCurrPage] = React.useState<number>(1)
  // 检索配置disabled
  const [retrievalConfigDisabled, setRetrievalConfigDisabled] = useState(false)
  // 检索配置
  const [retrievalConfig, setRetrievalConfig] = useState(
    currentDataset?.retrieval_model_dict as RetrievalConfig,
  )
  // 临时检索配置
  const [tempRetrievalConfig, setTempRetrievalConfig] = useState(
    cloneDeep(currentDataset?.retrieval_model_dict) as RetrievalConfig,
  )
  const retrievalMethodConfigRef = useRef<{
    resetConfig: (value: RetrievalConfig) => void
  }>()
  // 拖拽调整大小的控制方法
  const [leftPanelWidth, setLeftPanelWidth] = useState(MIN_LEFT_PANEL_WIDTH)
  const [queryPanelWidth, setQueryPanelWidth] = useState(
    INITIAL_QUERY_PANEL_WIDTH,
  )
  const [resultPanelWidth, setResultPanelWidth] = useState(
    INITIAL_RESULT_PANEL_WIDTH,
  )

  // 记录面板是否初始化过
  const [initialized, setInitialized] = useState(false)

  // 不使用useMemo缓存窗口总宽度，因为这会导致resize时无法获取最新宽度
  // const totalWidth = useMemo(() => window.innerWidth, []);

  // 调整左侧面板宽度 - 修改自身宽度，同时调整右侧面板宽度（中间面板固定）
  const resizeLeftPanel = useCallback(
    debounce((width: number) => {
      const totalWidth = window.innerWidth
      const newLeftWidth = Math.max(width, MIN_LEFT_PANEL_WIDTH)
      // 中间面板宽度固定为300px
      const fixedQueryWidth = MIN_QUERY_PANEL_WIDTH
      // 计算右侧面板的新宽度（总宽度减去左侧和中间面板宽度）
      const newResultWidth = Math.max(
        totalWidth - newLeftWidth - fixedQueryWidth,
        MIN_RESULT_PANEL_WIDTH,
      )

      setLeftPanelWidth(newLeftWidth)
      setQueryPanelWidth(fixedQueryWidth)
      setResultPanelWidth(newResultWidth)
    }, 100),
    [],
  )

  // 中间面板宽度保持固定，不提供调整功能
  const resizeQueryPanel = useCallback(
    debounce((width: number) => {
      // 中间面板宽度固定为300px，忽略调整
      setQueryPanelWidth(MIN_QUERY_PANEL_WIDTH)
    }, 100),
    [],
  )

  // 调整右侧面板宽度 - 修改自身宽度，同时调整左侧面板宽度（中间面板固定）
  const resizeResultPanel = useCallback(
    debounce((width: number) => {
      const totalWidth = window.innerWidth
      const newResultWidth = Math.max(width, MIN_RESULT_PANEL_WIDTH)
      // 中间面板宽度固定为300px
      const fixedQueryWidth = MIN_QUERY_PANEL_WIDTH
      // 计算左侧面板的新宽度（总宽度减去右侧和中间面板宽度）
      const newLeftWidth = Math.max(
        totalWidth - newResultWidth - fixedQueryWidth,
        MIN_LEFT_PANEL_WIDTH,
      )

      setResultPanelWidth(newResultWidth)
      setQueryPanelWidth(fixedQueryWidth)
      setLeftPanelWidth(newLeftWidth)
    }, 100),
    [],
  )

  // 初始化布局
  useEffect(() => {
    if (!initialized) {
      const totalWidth = window.innerWidth

      // 设置面板初始宽度 - 中间面板固定为300px
      const fixedQueryWidth = MIN_QUERY_PANEL_WIDTH // 固定中间面板宽度为300px
      const initialResultWidth = INITIAL_RESULT_PANEL_WIDTH
      const initialLeftWidth = Math.max(
        totalWidth - fixedQueryWidth - initialResultWidth,
        MIN_LEFT_PANEL_WIDTH,
      )

      setLeftPanelWidth(initialLeftWidth)
      setQueryPanelWidth(fixedQueryWidth)
      setResultPanelWidth(initialResultWidth)
      setInitialized(true)
    }
  }, [initialized])

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = debounce(() => {
      const totalWidth = window.innerWidth
      const currentPanelsWidth
        = leftPanelWidth + queryPanelWidth + resultPanelWidth

      // 如果面板总宽度不等于窗口宽度，则进行调整
      if (Math.abs(totalWidth - currentPanelsWidth) > 10) {
        // 中间面板宽度固定为300px
        const fixedQueryWidth = MIN_QUERY_PANEL_WIDTH

        // 保持左右面板的比例
        const leftRatio = leftPanelWidth / (leftPanelWidth + resultPanelWidth)
        const rightRatio
          = resultPanelWidth / (leftPanelWidth + resultPanelWidth)

        // 计算剩余可用空间
        const availableWidth = totalWidth - fixedQueryWidth

        // 按比例分配左右面板宽度
        let newLeftWidth = Math.floor(availableWidth * leftRatio)
        let newRightWidth = Math.floor(availableWidth * rightRatio)

        // 确保最小宽度
        newLeftWidth = Math.max(newLeftWidth, MIN_LEFT_PANEL_WIDTH)
        newRightWidth = Math.max(newRightWidth, MIN_RESULT_PANEL_WIDTH)

        // 如果两个面板宽度之和超过可用空间，则按比例缩小
        if (newLeftWidth + newRightWidth > availableWidth) {
          const excess = newLeftWidth + newRightWidth - availableWidth
          newLeftWidth -= Math.floor(excess * leftRatio)
          newRightWidth -= Math.floor(excess * rightRatio)

          // 再次确保最小宽度
          newLeftWidth = Math.max(newLeftWidth, MIN_LEFT_PANEL_WIDTH)
          newRightWidth = Math.max(newRightWidth, MIN_RESULT_PANEL_WIDTH)
        }

        setLeftPanelWidth(newLeftWidth)
        setQueryPanelWidth(fixedQueryWidth)
        setResultPanelWidth(newRightWidth)
      }
    }, 100)

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [leftPanelWidth, resultPanelWidth])

  const {
    data: recordsRes,
    error,
    mutate: recordsMutate,
  } = useSWR(
    {
      action: 'fetchTestingRecords',
      datasetId,
      params: { limit, page: currPage },
    },
    apiParams => fetchTestingRecords(omit(apiParams, 'action')),
  )

  const total = recordsRes?.total || 0

  // 点击召回测试卡片
  const onClickCard = (detail: HitTestingType) => {
    setCurrParagraph({ paraInfo: detail, showModal: true })
  }
  // 保存检索配置
  const handleSave = () => {
    if (
      !isReRankModelSelected({
        rerankDefaultModel,
        isRerankDefaultModelValid: !!isRerankDefaultModelValid,
        rerankModelList,
        retrievalConfig,
        indexMethod: currentDataset?.indexing_technique || '',
      })
    ) {
      Toast.notify({
        type: 'error',
        message: t('dataset.placeholder.rerankModel'),
      })
      return
    }
    setRetrievalConfig(
      ensureRerankModelSelected({
        rerankDefaultModel: rerankDefaultModel!,
        retrievalConfig: cloneDeep(tempRetrievalConfig),
        indexMethod: currentDataset?.indexing_technique || '',
      }),
    )
    // setRetrievalConfigDisabled(true)
    Toast.notify({
      type: 'success',
      message: t('common.actionMsg.saveSuccessfully'),
    })
  }
  // 取消保存检索配置
  const handleCancel = () => {
    setTempRetrievalConfig(cloneDeep(retrievalConfig))
    retrievalMethodConfigRef.current?.resetConfig(retrievalConfig)
    setRetrievalConfigDisabled(true)
  }
  // 提交检索文本
  const submitText = async () => {
    setSubmitLoading(true)
    const [e, res] = await asyncRunSafe<HitTestingResponse>(
      hitTesting({
        datasetId,
        queryText: text,
        retrieval_model: {
          ...retrievalConfig,
          search_method:
            currentDataset?.indexing_technique === 'economy'
              ? RETRIEVE_METHOD.keywordSearch
              : retrievalConfig.search_method,
        },
      }) as Promise<HitTestingResponse>,
    )
    if (!e) {
      setHitResult(res)
      recordsMutate()
    }
    setSubmitLoading(false)
  }

  // 表格列
  const columns: TableColumnsType<HitTestingRecord> = [
    {
      title: t('dataset.hit.source'),
      key: 'source',
      render: (_: any, record) => (
        <>
          <span className="capitalize truncate">
            {t(`dataset.hit.sourceValue.${record.source}`)}
          </span>
          <span className="capitalize truncate">
            {t(`dataset.hit.sourceValue.${record.source}`)}
          </span>
        </>
      ),
      width: 120,
      ellipsis: true,
    },
    {
      title: t('dataset.hit.text'),
      key: 'content',
      dataIndex: 'content',
      ellipsis: true,
    },
    {
      title: t('dataset.hit.time'),
      key: 'created_at',
      render: (_: any, record) =>
        formatTime(
          record.created_at,
          t('common.dateFormat.dateTime') as string,
        ),
      width: 190,
      ellipsis: true,
    },
  ]

  return (
    <div className={style.wrap}>
      {/* 左侧区域 */}
      <div className={cn('flex w-full h-full')}>
        {/* 左侧配置面板 - 自适应宽度 */}
        <ResizableBox
          className="min-w-[400px]"
          triggerNode={
            <div className={cn(style.resizeTrigger, '-right-1.5 z-10')}>
              <Divider
                className={style.verticalDivider}
                type="vertical"
              ></Divider>
            </div>
          }
          direction="horizontal"
          triggerDirection="right"
          minWidth={MIN_LEFT_PANEL_WIDTH}
          onResize={resizeLeftPanel}
          style={{ width: `${leftPanelWidth}px` }}
        >
          <Scrollbar className="w-full h-full">
            <div className="mx-6 py-6 shrink">
              {/* 检索配置 */}
              <div className={cn(style['left-title'], '!px-0')}>
                {t('dataset.action.setting')}
              </div>
              {useXIYANRag
                ? <RetrievalMethodConfigXiyan
                  ref={retrievalMethodConfigRef}
                  disabled={retrievalConfigDisabled}
                  value={tempRetrievalConfig}
                  onChange={setTempRetrievalConfig}
                />
                : <RetrievalMethodConfig
                  ref={retrievalMethodConfigRef}
                  disabled={retrievalConfigDisabled}
                  value={tempRetrievalConfig}
                  onChange={setTempRetrievalConfig}
                />
              }
              <div className="flex mt-3 mb-8 items-center gap-4">
                <Button
                  className="w-[92px]"
                  variant="primary"
                  onClick={handleSave}
                >
                  {t('common.operation.save')}
                </Button>
              </div>

              {/* 查询结果 */}
              <div className={cn(style['left-title'], '!px-0')}>
                {t('dataset.hit.recents')}
              </div>
              <Table
                size="middle"
                columns={columns}
                pagination={{
                  pageSize: limit,
                  position: ['none', 'none'],
                }}
                scroll={{ x: '300px' }}
                dataSource={recordsRes?.data}
                className="border-gray-G5 rounded border shrink-0"
                rowClassName="cursor-pointer"
                onRow={(record) => {
                  return {
                    onClick: () => {
                      setText(record.content)
                    },
                  }
                }}
              ></Table>
              <Pagination
                className="mt-3"
                align="center"
                current={currPage}
                hideOnSinglePage
                onChange={setCurrPage}
                total={total}
                pageSize={limit}
                showQuickJumper={false}
                showSizeChanger={false}
              ></Pagination>
            </div>
          </Scrollbar>
        </ResizableBox>

        {/* 中间查询面板 */}
        <ResizableBox
          className="min-w-[300px]"
          triggerNode={
            <div className={cn(style.resizeTrigger, '-right-1.5 z-10')}>
              <Divider
                className={style.verticalDivider}
                type="vertical"
              ></Divider>
            </div>
          }
          direction="horizontal"
          triggerDirection="right"
          minWidth={MIN_QUERY_PANEL_WIDTH}
          onResize={resizeQueryPanel}
          style={{ width: `${queryPanelWidth}px` }}
        >
          <div className="flex flex-col h-full mx-6 py-6 w-full">
            <div className={cn(style['left-title'], '!px-0')}>
              {t('dataset.hit.input')}
            </div>
            <Input.TextArea
              value={text}
              maxLength={250}
              onChange={e => setText(e.target.value)}
              className="!h-full shrink mb-4"
              placeholder={t('dataset.hit.textPlaceholder')!}
            ></Input.TextArea>
            <Button
              className="w-[92px] shrink-0"
              variant={'primary'}
              onClick={submitText}
            >
              {t('common.operation.confirm')}
            </Button>
          </div>
        </ResizableBox>

        {/* 右侧结果区域 */}
        <ResizableBox
          className="min-w-[300px]"
          triggerNode={
            <div className={cn(style.resizeTrigger, '-left-1.5 z-10')}>
              <Divider
                className={style.verticalDivider}
                type="vertical"
              ></Divider>
            </div>
          }
          direction="horizontal"
          triggerDirection="left"
          minWidth={MIN_RESULT_PANEL_WIDTH}
          onResize={resizeResultPanel}
          style={{ width: `${resultPanelWidth}px` }}
        >
          <div className="w-full h-full">
            <div className={style['right-title']}>
              {t('dataset.hit.result')}
            </div>
            {submitLoading
              ? (
                <div className={cn(style['right-content'], 'gap-3')}>
                  <SegmentCard
                    loading={true}
                    scene="hitTesting"
                    className="h-[216px]"
                  />
                  <SegmentCard
                    loading={true}
                    scene="hitTesting"
                    className="h-[216px]"
                  />
                </div>
              )
              : !hitResult?.records.length
                ? (
                  <Empty
                    text={t('dataset.hit.emptyTip')}
                    icon="/assets/hit-testing-empty.svg"
                  ></Empty>
                )
                : (
                  <Scrollbar className={cn(style['right-content'], 'gap-3')}>
                    {hitResult?.records.map((record, idx) => {
                      return (
                        <HitSegmentCard
                          key={idx}
                          detail={record.segment as any}
                          score={record.score}
                          onClick={() => onClickCard(record as any)}
                        />
                      )
                    })}
                  </Scrollbar>
                )}
          </div>
        </ResizableBox>
      </div>

      {currParagraph.showModal && (
        <SegmentDetail
          onCancel={() => setCurrParagraph({ showModal: false })}
          canEdit={false}
          segInfo={currParagraph.paraInfo?.segment}
        />
      )}
    </div>
  )
}

export default HitTesting
